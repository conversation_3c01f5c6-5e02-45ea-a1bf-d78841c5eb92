<?php
/**
 * Romanian Localization and Legal Domain Functions
 * Enhanced Romanian language support and legal terminology
 * Portal Judiciar România - DosareJust.ro
 */

/**
 * Romanian Localization Class
 * Comprehensive Romanian language and legal domain support
 */
class RomanianLocalization {
    
    /**
     * Romanian date and time formatting
     * @param string $date Date string
     * @param string $format Output format
     * @return string Formatted Romanian date
     */
    public static function formatRomanianDate($date, $format = 'd.m.Y') {
        if (empty($date)) {
            return '';
        }
        
        try {
            $dateObj = new DateTime($date);
            $formatted = $dateObj->format($format);
            
            // Add Romanian day names if needed
            if (strpos($format, 'l') !== false) {
                $dayNames = [
                    'Monday' => 'Luni',
                    'Tuesday' => 'Marți',
                    'Wednesday' => 'Miercuri',
                    'Thursday' => 'Joi',
                    'Friday' => 'Vineri',
                    'Saturday' => 'Sâmbătă',
                    'Sunday' => 'Duminică'
                ];
                
                $englishDay = $dateObj->format('l');
                if (isset($dayNames[$englishDay])) {
                    $formatted = str_replace($englishDay, $dayNames[$englishDay], $formatted);
                }
            }
            
            // Add Romanian month names if needed
            if (strpos($format, 'F') !== false) {
                $monthNames = [
                    'January' => 'Ianuarie',
                    'February' => 'Februarie',
                    'March' => 'Martie',
                    'April' => 'Aprilie',
                    'May' => 'Mai',
                    'June' => 'Iunie',
                    'July' => 'Iulie',
                    'August' => 'August',
                    'September' => 'Septembrie',
                    'October' => 'Octombrie',
                    'November' => 'Noiembrie',
                    'December' => 'Decembrie'
                ];
                
                $englishMonth = $dateObj->format('F');
                if (isset($monthNames[$englishMonth])) {
                    $formatted = str_replace($englishMonth, $monthNames[$englishMonth], $formatted);
                }
            }
            
            return $formatted;
        } catch (Exception $e) {
            return $date;
        }
    }
    
    /**
     * Get Romanian legal document types
     * @return array Document types in Romanian
     */
    public static function getLegalDocumentTypes() {
        return [
            'civil' => [
                'sentinta' => 'Sentință civilă',
                'hotarare' => 'Hotărâre civilă',
                'incheiere' => 'Încheiere civilă',
                'ordonanta' => 'Ordonanță președințială',
                'decizie' => 'Decizie civilă'
            ],
            'penal' => [
                'sentinta' => 'Sentință penală',
                'hotarare' => 'Hotărâre penală',
                'incheiere' => 'Încheiere penală',
                'ordonanta' => 'Ordonanță penală',
                'rechizitoriu' => 'Rechizitoriu'
            ],
            'administrativ' => [
                'sentinta' => 'Sentință administrativă',
                'hotarare' => 'Hotărâre administrativă',
                'incheiere' => 'Încheiere administrativă',
                'decizie' => 'Decizie administrativă'
            ],
            'comercial' => [
                'sentinta' => 'Sentință comercială',
                'hotarare' => 'Hotărâre comercială',
                'incheiere' => 'Încheiere comercială',
                'decizie' => 'Decizie comercială'
            ]
        ];
    }
    
    /**
     * Get Romanian legal procedure stages
     * @return array Procedure stages in Romanian
     */
    public static function getLegalProcedureStages() {
        return [
            'civil' => [
                'depunere_cerere' => 'Depunerea cererii de chemare în judecată',
                'citare_parti' => 'Citarea părților',
                'judecata_fond' => 'Judecata pe fond',
                'pronuntare' => 'Pronunțarea sentinței',
                'comunicare' => 'Comunicarea sentinței',
                'apel' => 'Apel',
                'recurs' => 'Recurs în casație',
                'executare' => 'Executarea hotărârii'
            ],
            'penal' => [
                'sesizare' => 'Sesizarea organelor de urmărire penală',
                'cercetare' => 'Cercetarea penală',
                'trimitere_judecata' => 'Trimiterea în judecată',
                'judecata_fond' => 'Judecata pe fond',
                'pronuntare' => 'Pronunțarea sentinței',
                'apel' => 'Apel',
                'recurs' => 'Recurs în casație',
                'executare' => 'Executarea pedepsei'
            ],
            'administrativ' => [
                'depunere_cerere' => 'Depunerea cererii de contencios administrativ',
                'citare_parti' => 'Citarea părților',
                'administrare_probe' => 'Administrarea probelor',
                'judecata_fond' => 'Judecata pe fond',
                'pronuntare' => 'Pronunțarea sentinței',
                'apel' => 'Apel',
                'recurs' => 'Recurs în casație'
            ]
        ];
    }
    
    /**
     * Get Romanian court roles and positions
     * @return array Court roles in Romanian
     */
    public static function getCourtRoles() {
        return [
            'judecator' => 'Judecător',
            'judecator_delegat' => 'Judecător delegat',
            'presedinte_complet' => 'Președinte de complet',
            'presedinte_instanta' => 'Președinte de instanță',
            'vicepresedinte' => 'Vicepreședinte',
            'judecator_raptor' => 'Judecător-raportor',
            'procuror' => 'Procuror',
            'procuror_sef' => 'Procuror șef',
            'avocat' => 'Avocat',
            'avocat_din_oficiu' => 'Avocat din oficiu',
            'grefier' => 'Grefier',
            'grefier_sef' => 'Grefier șef'
        ];
    }
    
    /**
     * Get Romanian legal terminology for search optimization
     * @return array SEO-optimized legal terms
     */
    public static function getSEOLegalTerms() {
        return [
            'primary_keywords' => [
                'portal judiciar România',
                'căutare dosare judecătorești',
                'dosare online România',
                'instanțe judecătorești România',
                'tribunal România',
                'curtea de apel',
                'judecătorie România',
                'procese judiciare',
                'ședințe judecată',
                'hotărâri judecătorești'
            ],
            'secondary_keywords' => [
                'sistem judiciar român',
                'justiția română',
                'căutare procese civile',
                'căutare procese penale',
                'contencios administrativ',
                'drept comercial România',
                'avocați România',
                'procedură civilă',
                'procedură penală',
                'executare silită'
            ],
            'long_tail_keywords' => [
                'cum caut un dosar judecătoresc în România',
                'verificare stadiu dosar tribunal',
                'căutare ședințe judecată după dată',
                'consultare dosare civile online',
                'informații procese penale România',
                'portal oficial instanțe România',
                'căutare hotărâri judecătorești',
                'verificare program ședințe tribunal'
            ],
            'location_keywords' => [
                'tribunal București',
                'curtea de apel Cluj',
                'judecătoria Constanța',
                'instanțe Iași',
                'tribunal Timișoara',
                'curtea de apel Craiova',
                'judecătorii București',
                'instanțe judecătorești Moldova',
                'tribunale Transilvania',
                'curți apel România'
            ]
        ];
    }
    
    /**
     * Generate Romanian legal meta description
     * @param string $pageType Type of page
     * @param array $context Page context data
     * @return string Optimized meta description
     */
    public static function generateLegalMetaDescription($pageType, $context = []) {
        $baseDescriptions = [
            'homepage' => 'Portal oficial pentru căutarea dosarelor judecătorești din România. Acces rapid la informații despre procese judiciare, părți implicate și stadiul dosarelor din toate instanțele românești: tribunale, judecătorii, curți de apel.',
            'search' => 'Căutați dosare judecătorești din toate instanțele României. Portal oficial cu acces la informații despre procese civile, penale, administrative și comerciale. Verificați stadiul dosarelor online.',
            'sessions' => 'Căutați ședințele de judecată programate în toate instanțele din România. Verificați programul tribunalelor, judecătoriilor și curților de apel. Informații actualizate despre ședințele publice de judecată.',
            'details' => 'Consultați detaliile complete ale dosarelor judecătorești din România. Informații despre părți, obiectul dosarului, stadiul procesului, ședințe programate și hotărâri judecătorești din toate instanțele românești.'
        ];
        
        $description = $baseDescriptions[$pageType] ?? $baseDescriptions['homepage'];
        
        // Add context-specific information
        if (isset($context['institution']) && !empty($context['institution'])) {
            $description = str_replace('toate instanțele românești', $context['institution'], $description);
        }
        
        if (isset($context['case_type']) && !empty($context['case_type'])) {
            $description = str_replace('procese judiciare', "procese {$context['case_type']}", $description);
        }
        
        return $description;
    }
    
    /**
     * Generate Romanian legal keywords
     * @param string $pageType Type of page
     * @param array $context Page context data
     * @return string Optimized keywords
     */
    public static function generateLegalKeywords($pageType, $context = []) {
        $seoTerms = self::getSEOLegalTerms();
        $keywords = [];
        
        // Add primary keywords
        $keywords = array_merge($keywords, array_slice($seoTerms['primary_keywords'], 0, 5));
        
        // Add page-specific keywords
        switch ($pageType) {
            case 'sessions':
                $keywords[] = 'ședințe judecată România';
                $keywords[] = 'program instanțe';
                $keywords[] = 'calendar ședințe judecată';
                break;
                
            case 'details':
                $keywords[] = 'detalii dosar judecătoresc';
                $keywords[] = 'informații proces';
                $keywords[] = 'stadiu dosar';
                break;
                
            case 'search':
                $keywords[] = 'căutare dosare online';
                $keywords[] = 'portal căutare procese';
                $keywords[] = 'verificare dosar tribunal';
                break;
        }
        
        // Add context-specific keywords
        if (isset($context['institution']) && !empty($context['institution'])) {
            $keywords[] = strtolower($context['institution']);
        }
        
        if (isset($context['location']) && !empty($context['location'])) {
            $keywords[] = "instanțe {$context['location']}";
            $keywords[] = "tribunal {$context['location']}";
        }
        
        // Add some secondary keywords
        $keywords = array_merge($keywords, array_slice($seoTerms['secondary_keywords'], 0, 3));
        
        return implode(', ', array_unique($keywords));
    }
}
