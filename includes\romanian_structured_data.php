<?php
/**
 * Romanian Legal Domain Structured Data
 * Enhanced Schema.org markup for Romanian legal services
 * Portal Judiciar România - DosareJust.ro
 */

require_once 'romanian_legal_domain.php';

/**
 * Generate comprehensive structured data for Romanian legal portal
 */
class RomanianStructuredData {
    
    /**
     * Generate Organization structured data for Romanian legal portal
     * @return array Organization schema
     */
    public static function getOrganizationSchema() {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'GovernmentOrganization',
            'name' => 'Portal Judiciar România - DosareJust.ro',
            'alternateName' => 'DosareJust',
            'description' => 'Portal oficial pentru căutarea dosarelor judecătorești din România. Acces rapid la informații despre procese judiciare din toate instanțele românești.',
            'url' => 'https://' . $_SERVER['HTTP_HOST'],
            'logo' => 'https://' . $_SERVER['HTTP_HOST'] . '/assets/images/logo.png',
            'image' => 'https://' . $_SERVER['HTTP_HOST'] . '/assets/images/portal-preview.png',
            'sameAs' => [
                'https://www.just.ro',
                'https://portal.just.ro'
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'RO',
                'addressRegion' => 'București',
                'addressLocality' => 'București',
                'streetAddress' => 'Strada Apolodor nr. 17, Sector 5',
                'postalCode' => '050741'
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'availableLanguage' => 'Romanian',
                'areaServed' => 'RO'
            ],
            'areaServed' => [
                '@type' => 'Country',
                'name' => 'România',
                'alternateName' => 'Romania'
            ],
            'serviceType' => 'Servicii judiciare online',
            'governmentType' => 'Sistem judiciar',
            'jurisdiction' => 'România'
        ];
    }
    
    /**
     * Generate WebSite structured data with Romanian search functionality
     * @return array WebSite schema
     */
    public static function getWebSiteSchema() {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Portal Judiciar România',
            'alternateName' => 'DosareJust.ro',
            'url' => 'https://' . $_SERVER['HTTP_HOST'],
            'description' => 'Portal pentru căutarea dosarelor judecătorești din România',
            'inLanguage' => 'ro-RO',
            'isAccessibleForFree' => true,
            'potentialAction' => [
                [
                    '@type' => 'SearchAction',
                    'target' => [
                        '@type' => 'EntryPoint',
                        'urlTemplate' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php?search={search_term_string}',
                        'actionPlatform' => [
                            'https://schema.org/DesktopWebPlatform',
                            'https://schema.org/MobileWebPlatform'
                        ]
                    ],
                    'query-input' => 'required name=search_term_string',
                    'name' => 'Căutare dosare judecătorești',
                    'description' => 'Căutați dosare judecătorești din toate instanțele României'
                ],
                [
                    '@type' => 'SearchAction',
                    'target' => [
                        '@type' => 'EntryPoint',
                        'urlTemplate' => 'https://' . $_SERVER['HTTP_HOST'] . '/sedinte.php?data={date_string}&institutie={institution_code}',
                        'actionPlatform' => [
                            'https://schema.org/DesktopWebPlatform',
                            'https://schema.org/MobileWebPlatform'
                        ]
                    ],
                    'query-input' => 'required name=date_string',
                    'name' => 'Căutare ședințe judecată',
                    'description' => 'Căutați ședințele de judecată programate în instanțele din România'
                ]
            ],
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Cetățeni români, avocați, justiția română',
                'geographicArea' => [
                    '@type' => 'Country',
                    'name' => 'România'
                ]
            ]
        ];
    }
    
    /**
     * Generate GovernmentService structured data for legal services
     * @return array GovernmentService schema
     */
    public static function getGovernmentServiceSchema() {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'GovernmentService',
            'name' => 'Căutare Dosare Judecătorești România',
            'description' => 'Serviciu online pentru căutarea și consultarea dosarelor judecătorești din toate instanțele României',
            'provider' => [
                '@type' => 'GovernmentOrganization',
                'name' => 'Ministerul Justiției România',
                'url' => 'https://www.just.ro'
            ],
            'areaServed' => [
                '@type' => 'Country',
                'name' => 'România'
            ],
            'availableChannel' => [
                '@type' => 'ServiceChannel',
                'serviceType' => 'Online',
                'serviceUrl' => 'https://' . $_SERVER['HTTP_HOST'],
                'availableLanguage' => 'ro-RO'
            ],
            'serviceType' => 'Servicii judiciare',
            'category' => 'Justiție și drept',
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Cetățeni, avocați, profesioniști în drept'
            ],
            'serviceOutput' => [
                '@type' => 'Thing',
                'name' => 'Informații dosare judecătorești',
                'description' => 'Detalii despre dosarele judecătorești, părți implicate, stadiul proceselor'
            ],
            'hoursAvailable' => [
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => [
                    'https://schema.org/Monday',
                    'https://schema.org/Tuesday',
                    'https://schema.org/Wednesday',
                    'https://schema.org/Thursday',
                    'https://schema.org/Friday',
                    'https://schema.org/Saturday',
                    'https://schema.org/Sunday'
                ],
                'opens' => '00:00',
                'closes' => '23:59'
            ],
            'isAccessibleForFree' => true
        ];
    }
    
    /**
     * Generate LocalBusiness structured data for court locations
     * @param string $courtCode Court institution code
     * @return array|null LocalBusiness schema
     */
    public static function getCourtLocationSchema($courtCode) {
        $courtHierarchy = RomanianLegalTerminology::getCourtHierarchy();
        $geographicData = RomanianLegalTerminology::getGeographicData();
        
        // Check if it's an appeal court
        if (isset($courtHierarchy['appeal_courts'][$courtCode])) {
            $court = $courtHierarchy['appeal_courts'][$courtCode];
            
            return [
                '@context' => 'https://schema.org',
                '@type' => 'Courthouse',
                'name' => $court['name'],
                'description' => 'Instanță de apel cu jurisdicție regională în sistemul judiciar român',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressCountry' => 'RO',
                    'addressLocality' => $court['location'],
                    'addressRegion' => $court['location']
                ],
                'areaServed' => array_map(function($county) {
                    return [
                        '@type' => 'AdministrativeArea',
                        'name' => $county . ', România'
                    ];
                }, $court['counties']),
                'serviceType' => 'Servicii judiciare de apel',
                'governmentType' => 'Instanță judecătorească',
                'jurisdiction' => implode(', ', $court['counties'])
            ];
        }
        
        return null;
    }
    
    /**
     * Generate BreadcrumbList structured data for Romanian legal portal
     * @param array $breadcrumbItems Breadcrumb items
     * @return array BreadcrumbList schema
     */
    public static function getBreadcrumbSchema($breadcrumbItems) {
        $listItems = [];
        
        foreach ($breadcrumbItems as $index => $item) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $item['name'],
                'item' => $item['url']
            ];
        }
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ];
    }
    
    /**
     * Generate FAQPage structured data for Romanian legal portal
     * @return array FAQPage schema
     */
    public static function getFAQSchema() {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => [
                [
                    '@type' => 'Question',
                    'name' => 'Cum pot căuta un dosar judecătoresc în România?',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => 'Puteți căuta dosare judecătorești folosind numele părților, numărul dosarului sau obiectul dosarului. Portalul oferă acces la informații din toate instanțele României: judecătorii, tribunale și curți de apel.'
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => 'Ce instanțe sunt acoperite de portal?',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => 'Portalul acoperă toate instanțele judecătorești din România: Înalta Curte de Casație și Justiție, toate Curțile de Apel, Tribunalele și Judecătoriile din țară.'
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => 'Cum pot verifica ședințele de judecată programate?',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => 'Accesați secțiunea "Ședințe Judecată" pentru a verifica programul ședințelor din toate instanțele României. Puteți filtra după dată și instanță.'
                    ]
                ],
                [
                    '@type' => 'Question',
                    'name' => 'Informațiile sunt actualizate în timp real?',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => 'Da, portalul se conectează direct la sistemele oficiale ale instanțelor pentru a oferi informații actualizate despre dosarele judecătorești și ședințele programate.'
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Generate complete structured data for a page
     * @param string $pageType Type of page (homepage, search, sessions, details)
     * @param array $additionalData Additional page-specific data
     * @return string JSON-LD structured data
     */
    public static function generatePageStructuredData($pageType, $additionalData = []) {
        $schemas = [];
        
        // Always include organization and website schemas
        $schemas[] = self::getOrganizationSchema();
        $schemas[] = self::getWebSiteSchema();
        $schemas[] = self::getGovernmentServiceSchema();
        
        // Add page-specific schemas
        switch ($pageType) {
            case 'homepage':
                $schemas[] = self::getFAQSchema();
                break;
                
            case 'court_details':
                if (isset($additionalData['court_code'])) {
                    $courtSchema = self::getCourtLocationSchema($additionalData['court_code']);
                    if ($courtSchema) {
                        $schemas[] = $courtSchema;
                    }
                }
                break;
        }
        
        // Add breadcrumb if provided
        if (isset($additionalData['breadcrumb_items'])) {
            $schemas[] = self::getBreadcrumbSchema($additionalData['breadcrumb_items']);
        }
        
        return json_encode($schemas, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}
