<?php
/**
 * Pagin<PERSON> de căutare a ședințelor de judecată
 * Portal Judiciar - Căutare Ședințe
 */

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['session_results']) && $_GET['session_results'] === '1') {
    // Includere DOAR fișierele necesare pentru export (fără header HTML)
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/SedinteService.php';

    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleSessionExcelExportOnly();
    } elseif ($_GET['export'] === 'pdf') {
        handleSessionPdfExportOnly();
    } elseif ($_GET['export'] === 'txt') {
        handleSessionTxtExportOnly();
    } else {
        // Default to TXT for backward compatibility
        handleSessionTxtExportOnly();
    }
    exit; // STOP - nu mai executăm nimic altceva
}

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/header.php';
require_once 'services/SedinteService.php';

// Cache busting headers to prevent duplicate navigation issues
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Inițializare variabile
$results = [];
$error = null;
$searchParams = [];
$totalResults = 0;
$hasSearchCriteria = false;

// Obținem lista instanțelor pentru dropdown
$institutii = getInstanteList();

// Procesare parametri de intrare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dataSedinta = isset($_POST['dataSedinta']) ? trim($_POST['dataSedinta']) : '';
    $institutie = isset($_POST['institutie']) && $_POST['institutie'] !== '' ? $_POST['institutie'] : null;

    // Verificare dacă s-a trimis cel puțin un criteriu de căutare
    $hasSearchCriteria = !empty($dataSedinta) || !empty($institutie);

    if ($hasSearchCriteria) {
        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            $error = $dateValidation['error'];
        } else {
            try {
                // Inițializăm serviciul de ședințe
                $sedinteService = new SedinteService();

                // Construim parametrii de căutare
                $searchParams = [
                    'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
                    'institutie' => $institutie
                ];

                // Efectuăm căutarea și detectăm dacă s-a folosit fallback
                $originalInstitutie = $institutie;
                $results = $sedinteService->cautareSedinte($searchParams);
                $totalResults = count($results);

                // Verificăm dacă s-a folosit fallback pentru instituție
                // Aceasta se întâmplă când API-ul SOAP nu acceptă codul instituției
                // și căutarea se face fără filtrul de instituție, apoi se filtrează local
                if (!empty($originalInstitutie)) {
                    // Testăm dacă codul instituției este problematic prin verificarea logurilor
                    $logFile = __DIR__ . '/logs/soap_sessions.log';
                    if (file_exists($logFile)) {
                        $recentLogs = file_get_contents($logFile);
                        if (strpos($recentLogs, "Institution code '{$originalInstitutie}' not valid for SOAP API") !== false) {
                            // S-a folosit fallback - afișăm notificare
                            $institutii = getInstanteList();
                            $institutionName = $institutii[$originalInstitutie] ?? $originalInstitutie;
                            $error = "Notă: Codul instituției '{$institutionName}' nu este recunoscut de API-ul SOAP pentru ședințe. Căutarea a fost efectuată fără filtrul de instituție și rezultatele au fost filtrate local.";
                        }
                    }
                }

            } catch (Exception $e) {
                $error = 'Eroare la căutarea ședințelor: ' . $e->getMessage();
                error_log('Eroare căutare ședințe: ' . $e->getMessage());
            }
        }
    } else {
        $error = 'Vă rugăm să introduceți cel puțin un criteriu de căutare (data ședinței sau instituția).';
    }
}

/**
 * Validează data în format românesc DD.MM.YYYY
 */
function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data ședinței este obligatorie.'];
    }

    // Verificăm formatul DD.MM.YYYY
    if (!preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        return ['valid' => false, 'date' => '', 'error' => 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)'];
    }

    $day = (int)$matches[1];
    $month = (int)$matches[2];
    $year = (int)$matches[3];

    // Verificăm validitatea datei
    if (!checkdate($month, $day, $year)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data introdusă nu este validă'];
    }

    // Verificăm limitele anului (nu acceptăm date prea vechi sau prea în viitor)
    $currentYear = (int)date('Y');
    if ($year < 1990 || $year > $currentYear + 5) {
        return ['valid' => false, 'date' => '', 'error' => "Anul trebuie să fie între 1990 și " . ($currentYear + 5)];
    }

    // Convertim la format YYYY-MM-DD pentru SOAP API
    $formattedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    return ['valid' => true, 'date' => $formattedDate, 'error' => ''];
}

/**
 * DEDICATED TXT EXPORT FUNCTION - NO HTML CONTAMINATION
 */
function handleSessionTxtExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            // Returnăm o pagină HTML cu mesaj de eroare în loc de JSON
            header('Content-Type: text/html; charset=UTF-8');
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eroare Export - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> Nu există rezultate pentru export</h4>
            <p>Nu au fost găsite ședințe pentru criteriile de căutare specificate.</p>
            <a href="sedinte.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la căutare
            </a>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.txt';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Setăm header-ele pentru TXT CURAT
        header('Content-Type: text/plain; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Content-Transfer-Encoding: 8bit');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm DOAR conținutul TXT cu informații despre instituție
        generateSessionTxtContent($allResults, $institutie);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea TXT: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION - TRUE XLSX FORMAT
 */
function handleSessionExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            // Returnăm o pagină HTML cu mesaj de eroare în loc de JSON
            header('Content-Type: text/html; charset=UTF-8');
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eroare Export Excel - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> Nu există rezultate pentru export Excel</h4>
            <p>Nu au fost găsite ședințe pentru criteriile de căutare specificate.</p>
            <a href="sedinte.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la căutare
            </a>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului în format Excel
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel cu informații despre instituție
        generateSessionExcelFile($allResults, $filename, $institutie);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED PDF EXPORT FUNCTION - TCPDF INTEGRATION
 */
function handleSessionPdfExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        if (empty($allResults)) {
            // Returnăm o pagină HTML de eroare dacă nu avem rezultate
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export PDF - Nu există rezultate</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-pdf text-danger mb-3" style="font-size: 3rem;"></i>
                        <h4 class="card-title">Nu există rezultate pentru export</h4>
                        <p class="card-text">Nu au fost găsite ședințe pentru criteriile specificate.</p>
                        <a href="sedinte.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Înapoi la căutare
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului în format PDF
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.pdf';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul PDF cu informații despre instituție
        generateSessionPdfFile($allResults, $filename, $institutie, $dataSedinta);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea PDF: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Generează conținutul TXT pentru export ședințe cu suport complet UTF-8
 */
function generateSessionTxtContent($results, $institutieParam = null) {
    // Asigurăm encoding-ul UTF-8
    mb_internal_encoding('UTF-8');

    // Obținem lista instituțiilor pentru mapare
    $institutii = getInstanteList();

    // Header pentru fișierul TXT cu caractere românești
    $content = "";
    $content .= "ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE\n";
    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";
    $content .= "Generat la: " . date('d.m.Y H:i:s') . "\n";
    $content .= "Total ședințe găsite: " . count($results) . "\n";

    // Adăugăm informații despre instituția căutată
    if (!empty($institutieParam)) {
        $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
        $content .= "Instituție căutată: " . $numeInstitutie . "\n";
    } else {
        $content .= "Instituție căutată: Toate instituțiile\n";
    }

    $content .= "═══════════════════════════════════════════════════════════════════════════════\n\n";

    foreach ($results as $index => $sedinta) {
        $content .= "┌─ ȘEDINȚA " . ($index + 1) . " " . str_repeat("─", 60) . "\n";
        $content .= "│\n";

        // Determinăm instituția pentru această ședință
        $institutieNume = 'N/A';
        if (!empty($institutieParam)) {
            // Dacă avem parametrul de căutare, folosim acela
            $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
        } elseif (!empty($sedinta->dosare)) {
            // Altfel, încercăm să găsim instituția din primul dosar
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->institutie)) {
                    $institutieNume = $institutii[$dosar->institutie] ?? $dosar->institutie;
                    break;
                }
            }
        }

        $content .= "│ 🏛️  Instituție: " . $institutieNume . "\n";
        $content .= "│ 🏢  Departament: " . ($sedinta->departament ?? 'N/A') . "\n";

        // Formatăm informațiile despre judecători pentru export
        $completText = $sedinta->complet ?? 'N/A';
        if ($completText !== 'N/A') {
            $judgeInfo = parseJudgeInformation($completText);
            $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
            $content .= "│ 👥  Complet: " . $formattedJudgeInfo . "\n";
        } else {
            $content .= "│ 👥  Complet: N/A\n";
        }

        $content .= "│ 📅  Data: " . ($sedinta->data ?? 'N/A') . "\n";
        $content .= "│ 🕐  Ora: " . ($sedinta->ora ?? 'N/A') . "\n";
        $content .= "│\n";

        if (!empty($sedinta->dosare)) {
            $content .= "│ 📁  Dosare programate:\n";
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $content .= "│     • " . $dosar->numar;
                    // Adăugăm instituția dosarului dacă diferă de cea principală
                    if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                        $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        if ($dosarInstitutie !== $institutieNume) {
                            $content .= " (" . $dosarInstitutie . ")";
                        }
                    }
                    $content .= "\n";
                }
            }
        } else {
            $content .= "│ ℹ️   Nu sunt dosare programate pentru această ședință\n";
        }

        $content .= "│\n";
        $content .= "└" . str_repeat("─", 70) . "\n\n";
    }

    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";
    $content .= "Exportat din Portal Judiciar - DosareJust.ro\n";
    $content .= "Pentru informații oficiale, consultați portal.just.ro\n";
    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";

    // Asigurăm că outputul este în UTF-8
    echo mb_convert_encoding($content, 'UTF-8', 'UTF-8');
}

/**
 * Generează fișierul Excel pentru export ședințe folosind PhpSpreadsheet
 */
function generateSessionExcelFile($results, $filename, $institutieParam = null) {
    require_once __DIR__ . '/vendor/autoload.php';

    try {
        // Obținem lista instituțiilor pentru mapare
        $institutii = getInstanteList();

        // Creăm un nou spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Setăm titlul foii
        $sheet->setTitle('Ședințe de Judecată');

        // Header-ul principal
        $sheet->setCellValue('A1', 'ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE');
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('007BFF');
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('FFFFFF');

        // Informații despre export
        $sheet->setCellValue('A2', 'Generat la: ' . date('d.m.Y H:i:s'));
        $sheet->setCellValue('A3', 'Total ședințe găsite: ' . count($results));

        // Adăugăm informații despre instituția căutată
        if (!empty($institutieParam)) {
            $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
            $sheet->setCellValue('A4', 'Instituție căutată: ' . $numeInstitutie);
        } else {
            $sheet->setCellValue('A4', 'Instituție căutată: Toate instituțiile');
        }

        $sheet->getStyle('A2:A4')->getFont()->setItalic(true);

        // Header-ul tabelului
        $headerRow = 6;
        $headers = ['Nr. Ședință', 'Instituție', 'Departament', 'Complet', 'Data', 'Ora', 'Dosare Programate'];
        $column = 'A';

        foreach ($headers as $header) {
            $sheet->setCellValue($column . $headerRow, $header);
            $sheet->getStyle($column . $headerRow)->getFont()->setBold(true);
            $sheet->getStyle($column . $headerRow)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('F8F9FA');
            $column++;
        }

        // Datele ședințelor
        $row = $headerRow + 1;
        foreach ($results as $index => $sedinta) {
            $sheet->setCellValue('A' . $row, $index + 1);

            // Determinăm instituția pentru această ședință
            $institutieNume = 'N/A';
            if (!empty($institutieParam)) {
                // Dacă avem parametrul de căutare, folosim acela
                $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
            } elseif (!empty($sedinta->dosare)) {
                // Altfel, încercăm să găsim instituția din primul dosar
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->institutie)) {
                        $institutieNume = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        break;
                    }
                }
            }

            $sheet->setCellValue('B' . $row, $institutieNume);
            $sheet->setCellValue('C' . $row, $sedinta->departament ?? 'N/A');

            // Formatăm informațiile despre judecători pentru export Excel
            $completText = $sedinta->complet ?? 'N/A';
            if ($completText !== 'N/A') {
                $judgeInfo = parseJudgeInformation($completText);
                $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
                $sheet->setCellValue('D' . $row, $formattedJudgeInfo);
            } else {
                $sheet->setCellValue('D' . $row, 'N/A');
            }

            $sheet->setCellValue('E' . $row, $sedinta->data ?? 'N/A');
            $sheet->setCellValue('F' . $row, $sedinta->ora ?? 'N/A');

            // Dosarele programate cu informații despre instituție
            $dosareList = '';
            if (!empty($sedinta->dosare)) {
                $dosareNumbers = [];
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->numar)) {
                        $dosarText = $dosar->numar;
                        // Adăugăm instituția dosarului dacă diferă de cea principală
                        if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                            $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                            if ($dosarInstitutie !== $institutieNume) {
                                $dosarText .= " (" . $dosarInstitutie . ")";
                            }
                        }
                        $dosareNumbers[] = $dosarText;
                    }
                }
                $dosareList = implode('; ', $dosareNumbers);
            } else {
                $dosareList = 'Nu sunt dosare programate';
            }
            $sheet->setCellValue('G' . $row, $dosareList);

            $row++;
        }

        // Ajustăm lățimea coloanelor
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Setăm header-ele pentru download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Generăm și trimitem fișierul
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        // În caz de eroare, generăm un CSV simplu
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo "Departament,Complet,Data,Ora,Dosare\n";

        foreach ($results as $sedinta) {
            $dosareList = '';
            if (!empty($sedinta->dosare)) {
                $dosareNumbers = [];
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->numar)) {
                        $dosareNumbers[] = $dosar->numar;
                    }
                }
                $dosareList = implode('; ', $dosareNumbers);
            }

            // Formatăm informațiile despre judecători pentru CSV fallback
            $completText = $sedinta->complet ?? '';
            if (!empty($completText)) {
                $judgeInfo = parseJudgeInformation($completText);
                $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
            } else {
                $formattedJudgeInfo = '';
            }

            echo '"' . ($sedinta->departament ?? '') . '",';
            echo '"' . $formattedJudgeInfo . '",';
            echo '"' . ($sedinta->data ?? '') . '",';
            echo '"' . ($sedinta->ora ?? '') . '",';
            echo '"' . $dosareList . '"' . "\n";
        }
    }
}

/**
 * Generează fișierul PDF pentru export ședințe folosind TCPDF
 */
function generateSessionPdfFile($results, $filename, $institutieParam = null, $dataSedinta = '') {
    // Configurare TCPDF
    define('K_TCPDF_EXTERNAL_CONFIG', true);

    // Verificăm dacă TCPDF este disponibil
    $tcpdfPath = __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';
    if (!file_exists($tcpdfPath)) {
        // Încercăm calea alternativă
        $tcpdfPath = dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/tcpdf.php';
        if (!file_exists($tcpdfPath)) {
            throw new Exception("TCPDF nu este instalat sau calea este incorectă. Căutat în: " . $tcpdfPath);
        }
    }

    // Includem TCPDF
    require_once $tcpdfPath;

    try {
        // Obținem lista instituțiilor pentru mapare
        $institutii = getInstanteList();

        // Creăm o instanță TCPDF
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Setăm informațiile documentului
        $pdf->SetCreator('Portal Judiciar');
        $pdf->SetAuthor('Portal Judiciar');
        $pdf->SetTitle('Ședințe de Judecată - ' . $dataSedinta);
        $pdf->SetSubject('Rezultate căutare ședințe judecată');
        $pdf->SetKeywords('ședințe, judecată, instanță, România');

        // Setăm marginile (2cm = 20mm)
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(15);

        // Setăm auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Eliminăm header și footer default
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Adăugăm prima pagină
        $pdf->AddPage();

        // Setăm fontul principal
        $pdf->SetFont('dejavusans', '', 10);

        // Header principal
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->SetTextColor(0, 123, 255); // Blue color #007bff
        $pdf->Cell(0, 15, 'ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE', 0, 1, 'C');

        $pdf->Ln(5);

        // Informații despre export
        $pdf->SetFont('dejavusans', '', 10);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 6, 'Generat la: ' . date('d.m.Y H:i:s'), 0, 1, 'L');
        $pdf->Cell(0, 6, 'Data căutată: ' . $dataSedinta, 0, 1, 'L');

        // Informații despre instituția căutată
        if (!empty($institutieParam)) {
            $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
            $pdf->Cell(0, 6, 'Instituție: ' . $numeInstitutie, 0, 1, 'L');
        } else {
            $pdf->Cell(0, 6, 'Instituție: Toate instituțiile', 0, 1, 'L');
        }

        $pdf->Cell(0, 6, 'Total ședințe găsite: ' . count($results), 0, 1, 'L');

        $pdf->Ln(10);

        // Continuăm cu tabelul în următoarea funcție pentru a respecta limita de 150 linii
        generateSessionPdfTable($pdf, $results, $institutii, $institutieParam);

        // Setăm header-ele pentru download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm și trimitem PDF-ul
        $pdf->Output($filename, 'D');

    } catch (Exception $e) {
        throw new Exception("Eroare la generarea PDF: " . $e->getMessage());
    }
}

/**
 * Generează tabelul cu ședințele în PDF
 */
function generateSessionPdfTable($pdf, $results, $institutii, $institutieParam) {
    // Header tabel
    $pdf->SetFont('dejavusans', 'B', 9);
    $pdf->SetFillColor(0, 123, 255); // Blue background #007bff
    $pdf->SetTextColor(255, 255, 255); // White text

    // Definim lățimile coloanelor optimizate (total 170mm pentru A4 cu margini de 20mm)
    // Ajustăm pentru a preveni overflow-ul de text
    $colWidths = [12, 22, 30, 40, 66]; // Nr, Data/Ora, Departament, Complet, Dosare

    // Header-ul tabelului
    $pdf->Cell($colWidths[0], 8, 'Nr.', 1, 0, 'C', true);
    $pdf->Cell($colWidths[1], 8, 'Data/Ora', 1, 0, 'C', true);
    $pdf->Cell($colWidths[2], 8, 'Departament', 1, 0, 'C', true);
    $pdf->Cell($colWidths[3], 8, 'Complet', 1, 0, 'C', true);
    $pdf->Cell($colWidths[4], 8, 'Dosare Programate', 1, 1, 'C', true);

    // Resetăm culoarea textului pentru conținut
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('dejavusans', '', 8);

    // Datele ședințelor
    foreach ($results as $index => $sedinta) {
        // Preparăm datele pentru această ședință
        $nr = $index + 1;
        $dataOra = ($sedinta->data ?? 'N/A') . "\n" . ($sedinta->ora ?? 'N/A');
        $departament = $sedinta->departament ?? 'N/A';

        // Formatăm informațiile despre judecători pentru export PDF
        $completText = $sedinta->complet ?? 'N/A';
        if ($completText !== 'N/A') {
            $judgeInfo = parseJudgeInformation($completText);
            $complet = formatJudgeInformationForExport($judgeInfo);
        } else {
            $complet = 'N/A';
        }

        // Preparăm lista dosarelor
        $dosareText = '';
        if (!empty($sedinta->dosare)) {
            $dosareNumbers = [];
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $dosarText = $dosar->numar;
                    // Adăugăm instituția dosarului dacă diferă de cea principală
                    if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                        $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        // Determinăm instituția principală pentru comparație
                        $institutieNume = 'N/A';
                        if (!empty($institutieParam)) {
                            $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
                        }
                        if ($dosarInstitutie !== $institutieNume) {
                            $dosarText .= " (" . $dosarInstitutie . ")";
                        }
                    }
                    $dosareNumbers[] = $dosarText;
                }
            }
            $dosareText = implode('; ', $dosareNumbers);
        } else {
            $dosareText = 'Nu sunt dosare programate';
        }

        // Calculăm înălțimea necesară pentru fiecare coloană cu text multi-linie
        $minHeight = 8;  // Înălțimea minimă pentru o celulă

        // Calculăm înălțimea pentru fiecare coloană folosind getStringHeight
        $dataOraHeight = $pdf->getStringHeight($colWidths[1], $dataOra);
        $departamentHeight = $pdf->getStringHeight($colWidths[2], $departament);
        $completHeight = $pdf->getStringHeight($colWidths[3], $complet);
        $dosareHeight = $pdf->getStringHeight($colWidths[4], $dosareText);

        // Înălțimea maximă necesară (cu padding suplimentar pentru alinierea corectă)
        $maxHeight = max($minHeight, $dataOraHeight + 2, $departamentHeight + 2, $completHeight + 2, $dosareHeight + 2);

        // Verificăm dacă avem loc pe pagină
        if ($pdf->GetY() + $maxHeight > $pdf->getPageHeight() - 25) {
            $pdf->AddPage();
        }

        // Salvăm poziția Y curentă
        $startY = $pdf->GetY();
        $startX = $pdf->GetX();

        // Metoda îmbunătățită: folosim Rect pentru contur și MultiCell cu poziționare precisă

        // 1. Desenăm conturul tabelului folosind Rect pentru control total
        $pdf->SetDrawColor(0, 0, 0); // Culoare neagră pentru contur
        $pdf->SetLineWidth(0.2);

        // Desenăm conturul fiecărei celule
        $currentX = $startX;
        for ($i = 0; $i < 5; $i++) {
            $pdf->Rect($currentX, $startY, $colWidths[$i], $maxHeight);
            $currentX += $colWidths[$i];
        }

        // 2. Adăugăm conținutul cu alinierea corectă în fiecare celulă

        // Celula Nr - centrat vertical și orizontal
        $cellY = $startY + ($maxHeight - 4) / 2; // Centrare verticală pentru text simplu
        $pdf->SetXY($startX, $cellY);
        $pdf->Cell($colWidths[0], 4, $nr, 0, 0, 'C');

        // Celula Data/Ora - centrat orizontal, centrat vertical
        $cellX = $startX + $colWidths[0];
        $cellY = $startY + ($maxHeight - $dataOraHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY); // +1 pentru padding
        $pdf->MultiCell($colWidths[1] - 2, 4, $dataOra, 0, 'C', false, 0);

        // Celula Departament - aliniat stânga, centrat vertical
        $cellX = $startX + $colWidths[0] + $colWidths[1];
        $cellY = $startY + ($maxHeight - $departamentHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[2] - 2, 4, $departament, 0, 'L', false, 0);

        // Celula Complet - aliniat stânga, centrat vertical
        $cellX = $startX + $colWidths[0] + $colWidths[1] + $colWidths[2];
        $cellY = $startY + ($maxHeight - $completHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[3] - 2, 4, $complet, 0, 'L', false, 0);

        // Celula Dosare - aliniat stânga, aliniat sus cu padding
        $cellX = $startX + $colWidths[0] + $colWidths[1] + $colWidths[2] + $colWidths[3];
        $cellY = $startY + 1; // Padding de sus pentru liste
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[4] - 2, 4, $dosareText, 0, 'L', false, 0);

        // Setăm poziția pentru următoarea linie
        $pdf->SetXY($startX, $startY + $maxHeight);
    }

    // Footer
    $pdf->Ln(10);
    $pdf->SetFont('dejavusans', 'I', 8);
    $pdf->SetTextColor(108, 117, 125); // Gray color
    $pdf->Cell(0, 6, 'Exportat din Portal Judiciar - DosareJust.ro', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Pentru informații oficiale, consultați portal.just.ro', 0, 1, 'C');

    // Numerotare pagini
    $pdf->SetY(-15);
    $pdf->SetFont('dejavusans', '', 8);
    $pdf->Cell(0, 10, 'Pagina ' . $pdf->getAliasNumPage() . ' din ' . $pdf->getAliasNbPages(), 0, 0, 'C');
}
?>

<!-- Cache busting meta tags to prevent duplicate navigation -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">

<!-- Stiluri pentru loading overlay pentru căutare ședințe -->
<style>
/* Loading overlay pentru căutare ședințe */
.session-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.session-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.session-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.session-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: sessionLoadingSpin 1s linear infinite;
}

.session-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.session-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes sessionLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .session-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .session-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .session-loading-message {
        font-size: 0.9rem;
    }

    .session-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.session-main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.session-main-content.loaded {
    opacity: 1;
}

/* Navigation Bar Styles */
.navbar {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 600;
    color: #2c3e50 !important;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: #007bff !important;
    transform: translateY(-1px);
}

.navbar-brand i {
    color: #007bff;
    margin-right: 0.5rem;
}

.navbar-nav .nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: #ffffff !important;
    background-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.navbar-nav .nav-link i {
    margin-right: 0.25rem;
}

/* Responsive navigation */
@media (max-width: 991.98px) {
    .navbar-nav {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
}

@media (max-width: 575.98px) {
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-nav .nav-link {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

/* Notification Container */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification-container .alert {
    margin-bottom: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    border-radius: 8px;
}

/* Modern Card Styles */
.streamlined-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: #ffffff;
}

.streamlined-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.streamlined-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem 1.5rem;
}

.streamlined-card .card-body {
    padding: 2rem 2.5rem;
}

.compact-form {
    padding: 1rem 0;
}

.compact-form .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.compact-form .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    min-height: 48px;
}

.compact-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.compact-form .mb-3 {
    margin-bottom: 2rem !important;
}

.compact-form .form-text {
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: #6c757d;
}

/* Searchable Select Styles */
.searchable-select-container {
    position: relative;
    margin-bottom: 1rem;
}

.searchable-select-input {
    border-radius: 8px;
    border: 2px solid #ced4da;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    background: #ffffff;
    font-size: 1rem;
    min-height: 48px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.searchable-select-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.searchable-select-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

.searchable-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 2px solid #007bff;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    margin-top: -2px;
}

.searchable-select-dropdown.show {
    display: block;
}

.dropdown-item {
    padding: 1rem 1.25rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    line-height: 1.4;
}

.dropdown-item:hover,
.dropdown-item.highlighted {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.dropdown-item.selected {
    background-color: #007bff;
    color: #ffffff;
    font-weight: 500;
}

.dropdown-item:last-child {
    border-bottom: none;
    border-radius: 0 0 6px 6px;
}

/* Enhanced visibility for institution field */
.institution-field-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.institution-field-container .form-label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.institution-field-container .form-text {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
}

/* Modern Session Results */
.sessions-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.session-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.session-item:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    border-color: #007bff;
}

.session-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.session-number {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.session-number i {
    font-size: 1.25rem;
}

.session-index {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.session-time {
    text-align: right;
}

.session-date {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.session-hour {
    display: block;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.session-details {
    margin-bottom: 1.25rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

.session-cases-modern {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
}

.cases-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #2c3e50;
}

.cases-count {
    font-size: 0.9rem;
}

.cases-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.case-link {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #ffffff;
    border: 2px solid #007bff;
    border-radius: 8px;
    color: #007bff;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.case-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.case-link:hover::before {
    left: 100%;
}

.case-link:hover {
    background: #007bff;
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.case-link:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.case-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.cases-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-style: italic;
}

/* Streamlined Sessions Table Layout */
.sessions-table-container {
    margin-top: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.sessions-table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.sessions-table thead th {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.sessions-table tbody tr {
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.sessions-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.001);
}

.sessions-table tbody tr:last-child {
    border-bottom: none;
}

.sessions-table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
}

/* Column specific styling - Optimized for enhanced Dosare Programate */
.session-number-col {
    width: 6%;
    text-align: center;
}

.session-datetime-col {
    width: 18%;
}

.session-department-col {
    width: 22%;
}

.session-complet-col {
    width: 18%;
}

.session-cases-col {
    width: 36%; /* Increased width for enhanced layout */
    padding: 0.75rem !important;
}

.session-number {
    text-align: center;
}

.session-index {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    min-width: 24px;
}

.datetime-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.session-date {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.session-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.session-department,
.session-complet {
    color: #2c3e50;
    font-weight: 500;
}

/* Enhanced Judge Information Display */
.judge-composition {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.judge-list {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.judge-list li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.85rem;
}

.judge-list li:last-child {
    border-bottom: none;
}

.judge-role {
    font-weight: 500;
    color: #007bff;
}

.judge-name {
    color: #2c3e50;
    font-weight: 400;
}

/* Enhanced Dosare Programate Layout */
.cases-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.cases-header-enhanced {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e9ecef;
}

.cases-count-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #6c757d;
    padding: 0.125rem 0.5rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid #dee2e6;
    order: 2;
}

.cases-priority-indicator {
    display: inline-flex;
    align-items: center;
    color: #007bff;
    font-size: 0.8rem;
    font-weight: 600;
    order: 1;
}

.cases-list-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.375rem;
    margin-top: 0.25rem;
}

.case-link-enhanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    color: white;
    text-decoration: none;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    transition: all 0.2s ease;
    line-height: 1.2;
    text-align: center;
    min-height: 28px;
    border: none;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.case-link-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.case-link-enhanced:hover::before {
    left: 100%;
}

.case-link-enhanced:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
}

.case-link-enhanced:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

.no-cases {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-style: italic;
    font-size: 0.8rem;
}

.no-cases-enhanced {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px dashed #dee2e6;
    border-radius: 8px;
    color: #6c757d;
    font-style: italic;
    font-size: 0.85rem;
    text-align: center;
}

/* Empty State */
.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state i {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

/* Desktop styling for date input buttons */
.input-group-append {
    display: flex;
    flex-direction: row;
}

.input-group-append .btn {
    border-radius: 0;
    border-left: none;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    white-space: nowrap;
}

.input-group-append .btn:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.input-group-append .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.input-group-append .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* ===== RESPONSIVE DESIGN FOR MOBILE ===== */

/* Mobile-specific styles for session search */
@media (max-width: 767px) {
    /* Container adjustments */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Card adjustments for mobile */
    .streamlined-card .card-body {
        padding: 1.5rem 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    /* Form adjustments */
    .compact-form .mb-3 {
        margin-bottom: 1.5rem !important;
    }

    .compact-form .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px; /* Touch-friendly size */
        padding: 1rem;
    }

    .searchable-select-input {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px;
        padding: 1rem;
    }

    .institution-field-container {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .btn {
        min-height: 48px;
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Date input buttons responsive styling */
    .input-group-append {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-top: 0.5rem;
    }

    .input-group-append .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        min-height: 44px;
    }

    .input-group-append .btn:last-child {
        margin-bottom: 0;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    /* Session card mobile layout */
    .session-card {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 6px;
    }

    .session-header {
        padding-bottom: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .session-title {
        font-size: 1.1rem;
    }

    .session-info {
        display: block; /* Stack items vertically on mobile */
        gap: 0;
    }

    .session-info-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .session-info-item:last-child {
        margin-bottom: 0;
    }

    .session-info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
        min-width: auto;
    }

    .session-info-value {
        font-size: 0.95rem;
        font-weight: 500;
        color: #2c3e50;
    }

    /* Session cases mobile layout */
    .session-cases {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }

    .session-cases-title {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .session-case-item {
        padding: 0.5rem 0;
    }

    .session-case-link {
        font-size: 0.9rem;
        word-break: break-all; /* Ensure long case numbers break properly */
    }

    /* Mobile case links */
    .case-link {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem;
        font-size: 1rem;
        margin-bottom: 0.75rem;
        min-height: 48px;
    }

    .case-link i {
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }

    /* Mobile table layout */
    .sessions-table-container {
        margin-top: 1rem;
        border-radius: 6px;
    }

    .sessions-table {
        font-size: 0.8rem;
    }

    .sessions-table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.7rem;
    }

    .sessions-table td {
        padding: 0.75rem 0.5rem;
    }

    /* Hide some columns on mobile */
    .session-department-col,
    .session-complet-col {
        display: none;
    }

    .session-number-col {
        width: 15%;
    }

    .session-datetime-col {
        width: 35%;
    }

    .session-cases-col {
        width: 60%; /* Increased for enhanced layout */
        padding: 0.5rem !important;
    }

    .datetime-container {
        gap: 0.125rem;
    }

    .session-date {
        font-size: 0.8rem;
    }

    .session-time {
        font-size: 0.7rem;
    }

    .cases-count-badge {
        font-size: 0.7rem;
        padding: 0.125rem 0.375rem;
    }

    /* Enhanced mobile layout for Judge Information */
    .judge-composition {
        font-size: 0.8rem;
        margin-bottom: 0.375rem;
    }

    .judge-list li {
        padding: 0.2rem 0;
        font-size: 0.75rem;
    }

    .judge-role {
        display: block;
        margin-bottom: 0.1rem;
    }

    .judge-name {
        display: block;
        padding-left: 0.5rem;
        font-style: italic;
    }

    /* Enhanced mobile layout for Dosare Programate */
    .cases-container {
        gap: 0.5rem;
        padding: 0.25rem 0;
    }

    .cases-header-enhanced {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        margin-bottom: 0.375rem;
    }

    .cases-priority-indicator {
        font-size: 0.75rem;
        order: 1;
    }

    .cases-count-badge {
        font-size: 0.65rem;
        padding: 0.125rem 0.375rem;
        order: 2;
        align-self: flex-end;
    }

    .cases-list-enhanced {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.25rem;
    }

    .case-link-enhanced {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
        min-height: 44px; /* Touch-friendly size */
        border-radius: 4px;
    }

    .case-link-compact {
        font-size: 0.7rem;
        padding: 0.125rem 0.375rem;
        margin-bottom: 0.125rem;
    }

    .no-cases {
        font-size: 0.7rem;
    }

    .no-cases-enhanced {
        padding: 0.75rem;
        font-size: 0.75rem;
    }

    /* Export buttons mobile layout */
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }

    /* Search results header mobile */
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .h5 {
        margin-bottom: 1rem;
    }

    /* Input group mobile adjustments */
    .input-group {
        flex-wrap: wrap;
    }

    .input-group .form-control {
        flex: 1 1 100%;
        margin-bottom: 0.5rem;
    }

    .input-group-append {
        flex: 1 1 100%;
    }

    .input-group-append .btn {
        width: 100%;
        border-radius: 6px;
    }

    /* Alert adjustments for mobile */
    .alert {
        margin-bottom: 1rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Information section mobile */
    .card-body .row {
        margin: 0;
    }

    .card-body .col-md-6 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .card-body .col-md-6:last-child {
        margin-bottom: 0;
    }

    /* No results section mobile */
    .text-center.py-5 {
        padding: 2rem 1rem !important;
    }

    .text-center.py-5 i {
        font-size: 3rem !important;
    }

    .text-center.py-5 h3 {
        font-size: 1.25rem;
        margin-top: 1rem !important;
    }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 991px) {
    .session-info {
        grid-template-columns: repeat(2, 1fr);
    }

    .session-info-item {
        flex-direction: row;
        align-items: center;
    }

    .session-info-label {
        min-width: 100px;
    }

    /* Enhanced Dosare Programate for tablets */
    .cases-list-enhanced {
        grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
        gap: 0.3rem;
    }

    .case-link-enhanced {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        min-height: 40px;
    }

    .session-cases-col {
        width: 55%;
    }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }

    .session-info {
        grid-template-columns: repeat(4, 1fr);
    }

    .session-card {
        padding: 2rem;
    }

    /* Enhanced Dosare Programate for large screens */
    .cases-list-enhanced {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 0.5rem;
    }

    .case-link-enhanced {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
        min-height: 42px;
    }

    .cases-container {
        gap: 1rem;
        padding: 0.75rem 0;
    }

    .cases-header-enhanced {
        margin-bottom: 0.75rem;
    }
}

/* Print styles */
@media print {
    .judicial-navbar,
    .btn,
    .btn-group,
    .alert,
    .card-header,
    #sessionLoadingOverlay {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .session-card {
        border: 1px solid #000 !important;
        margin-bottom: 1rem !important;
        page-break-inside: avoid;
    }

    .session-title {
        color: #000 !important;
    }

    .session-info-label,
    .session-info-value {
        color: #000 !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .session-card {
        border: 2px solid #000;
    }

    .session-header {
        border-bottom: 3px solid #000;
    }

    .session-info-item {
        border: 1px solid #000;
    }

    /* Enhanced Dosare Programate high contrast */
    .case-link-enhanced {
        border: 2px solid #000;
        background: #000;
        color: #fff;
    }

    .case-link-enhanced:hover {
        background: #333;
        border-color: #666;
    }

    .cases-header-enhanced {
        border-bottom: 2px solid #000;
    }

    .cases-count-badge {
        border: 1px solid #000;
        background: #f0f0f0;
        color: #000;
    }

    .no-cases-enhanced {
        border: 2px solid #000;
        background: #f0f0f0;
        color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .session-card,
    .nav-link,
    .btn,
    .session-loading-overlay,
    .case-link-enhanced,
    .case-link-enhanced::before {
        transition: none !important;
        animation: none !important;
    }

    .case-link-enhanced:hover {
        transform: none !important;
    }
}
</style>

<!-- Loading overlay pentru căutare ședințe -->
<div id="sessionLoadingOverlay" class="session-loading-overlay" style="display: none;">
    <div class="session-loading-content">
        <div class="session-loading-spinner"></div>
        <p class="session-loading-message">Se caută ședințele...</p>
        <p class="session-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<!-- Notification Container -->
<div id="notificationContainer" class="notification-container" style="display: none;">
    <div id="notification" class="alert" role="alert"></div>
</div>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-gavel me-2"></i>
            DosareJust.ro - Portal Judiciar
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-search me-1"></i>
                        Căutare Dosare
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="sedinte.php">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Ședințe
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <!-- Main Search Form -->
            <div class="card streamlined-card compact-form">
                <div class="card-header">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Căutare Ședințe de Judecată
                    </h1>
                </div>
                <div class="card-body">
                    <p class="mb-3 text-muted">
                        Căutați ședințele de judecată programate pe o anumită dată și/sau la o anumită instituție.
                    </p>

                    <form method="POST" action="sedinte.php" id="sessionSearchForm">
                        <div class="row">
                            <!-- Data Ședinței -->
                            <div class="col-md-6 mb-3">
                                <label for="dataSedinta" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Data Ședinței <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control"
                                           id="dataSedinta"
                                           name="dataSedinta"
                                           placeholder="ZZ.LL.AAAA (ex: 15.03.2024)"
                                           value="<?php echo htmlspecialchars($_POST['dataSedinta'] ?? ''); ?>"
                                           required
                                           pattern="^(\d{1,2})\.(\d{1,2})\.(\d{4})$"
                                           title="Formatul datei trebuie să fie ZZ.LL.AAAA">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary" id="todayBtn" title="Setează data de azi">
                                            <i class="fas fa-calendar-day"></i>
                                            Azi
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="tomorrowBtn" title="Setează data de mâine">
                                            <i class="fas fa-calendar-plus"></i>
                                            Mâine
                                        </button>
                                    </div>
                                </div>
                                <div class="form-text">
                                    Introduceți data în format românesc (ZZ.LL.AAAA)
                                </div>
                            </div>

                            <!-- Instituția -->
                            <div class="col-md-6 mb-3">
                                <div class="institution-field-container">
                                    <label for="institutie" class="form-label">
                                        <i class="fas fa-university me-2"></i>
                                        Instituția
                                    </label>
                                    <div class="searchable-select-container">
                                        <input type="text"
                                               class="form-control searchable-select-input"
                                               id="institutieSearch"
                                               placeholder="Căutați sau selectați o instituție..."
                                               autocomplete="off">
                                        <select class="form-control d-none" id="institutie" name="institutie">
                                            <option value="">-- Toate instituțiile --</option>
                                            <?php foreach ($institutii as $cod => $nume): ?>
                                                <option value="<?php echo htmlspecialchars($cod); ?>"
                                                        <?php echo (isset($_POST['institutie']) && $_POST['institutie'] === $cod) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($nume); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="searchable-select-dropdown" id="institutieDropdown"></div>
                                    </div>
                                    <div class="form-text">
                                        Opțional: căutați și selectați o instituție specifică
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>
                                        Caută Ședințe
                                    </button>
                                    <button type="reset" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-eraser me-2"></i>
                                        Resetează
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

                <?php if ($error): ?>
                <!-- Error Message -->
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

            <?php if ($hasSearchCriteria && !$error): ?>
            <!-- Search Results -->
            <div class="card streamlined-card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        Ședințe găsite
                        <?php if ($totalResults > 0): ?>
                            <span class="badge bg-primary ms-2"><?php echo $totalResults; ?></span>
                        <?php endif; ?>
                    </h2>

                    <?php if ($totalResults > 0): ?>
                    <!-- Export Buttons -->
                    <div class="btn-group" role="group" aria-label="Opțiuni export">
                        <a href="sedinte.php?export=txt&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-file-alt me-1"></i>
                            Export TXT
                        </a>
                        <a href="sedinte.php?export=pdf&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                           class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-file-pdf me-1"></i>
                            Export PDF
                        </a>
                        <a href="sedinte.php?export=xlsx&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-excel me-1"></i>
                            Export Excel
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($totalResults === 0): ?>
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-calendar-times text-muted mb-3"></i>
                            <h4 class="text-muted mb-2">Nu au fost găsite ședințe</h4>
                            <p class="text-muted mb-0">
                                Nu există ședințe programate pentru criteriile de căutare specificate.
                            </p>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- Modern Minimalist Results -->
                    <!-- Streamlined Table Layout -->
                    <div class="sessions-table-container">
                        <div class="table-responsive">
                            <table class="table table-hover sessions-table">
                                <thead>
                                    <tr>
                                        <th scope="col" class="session-number-col">#</th>
                                        <th scope="col" class="session-datetime-col">Data și Ora</th>
                                        <th scope="col" class="session-department-col">Departament</th>
                                        <th scope="col" class="session-complet-col">Complet</th>
                                        <th scope="col" class="session-cases-col">Dosare Programate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results as $index => $sedinta): ?>
                                    <tr class="session-row">
                                        <td class="session-number">
                                            <span class="session-index"><?php echo $index + 1; ?></span>
                                        </td>
                                        <td class="session-datetime">
                                            <div class="datetime-container">
                                                <div class="session-date">
                                                    <i class="fas fa-calendar-alt text-primary me-1"></i>
                                                    <?php echo htmlspecialchars($sedinta->data ?? 'N/A'); ?>
                                                </div>
                                                <div class="session-time">
                                                    <i class="fas fa-clock text-muted me-1"></i>
                                                    <?php echo htmlspecialchars($sedinta->ora ?? 'N/A'); ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="session-department">
                                            <?php echo htmlspecialchars($sedinta->departament ?? 'N/A'); ?>
                                        </td>
                                        <td class="session-complet">
                                            <?php
                                            $completText = $sedinta->complet ?? 'N/A';
                                            if ($completText !== 'N/A') {
                                                $judgeInfo = parseJudgeInformation($completText);
                                                echo formatJudgeInformation($judgeInfo);
                                            } else {
                                                echo '<span class="text-muted">N/A</span>';
                                            }
                                            ?>
                                        </td>
                                        <td class="session-cases">
                                            <?php if (!empty($sedinta->dosare)): ?>
                                            <div class="cases-container">
                                                <div class="cases-header-enhanced">
                                                    <div class="cases-priority-indicator">
                                                        <i class="fas fa-gavel me-1"></i>
                                                        Dosare programate
                                                    </div>
                                                    <div class="cases-count-badge">
                                                        <?php echo count($sedinta->dosare); ?> dosare
                                                    </div>
                                                </div>
                                                <div class="cases-list-enhanced">
                                                    <?php foreach ($sedinta->dosare as $dosar): ?>
                                                        <?php if ($dosar && !empty($dosar->numar)): ?>
                                                        <?php
                                                        // Use the institution from the search form if available, otherwise use the case institution
                                                        $institutieParam = '';
                                                        if (!empty($_POST['institutie'])) {
                                                            $institutieParam = $_POST['institutie'];
                                                        } elseif (!empty($dosar->institutie)) {
                                                            $institutieParam = $dosar->institutie;
                                                        }
                                                        ?>
                                                        <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($institutieParam); ?>"
                                                           class="case-link-enhanced"
                                                           title="Vizualizează detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                                           data-case-number="<?php echo htmlspecialchars($dosar->numar); ?>"
                                                           data-institution="<?php echo htmlspecialchars($institutieParam); ?>">
                                                            <?php echo htmlspecialchars($dosar->numar); ?>
                                                        </a>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                            <?php else: ?>
                                            <div class="no-cases-enhanced">
                                                <i class="fas fa-info-circle text-muted me-2"></i>
                                                <span class="text-muted">Nu sunt dosare programate</span>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Information Section -->
            <div class="card streamlined-card mt-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informații despre Portal
                    </h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h6 text-primary">Despre Căutarea Ședințelor</h3>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Căutare după data ședinței</li>
                                <li><i class="fas fa-check text-success me-2"></i>Filtrare după instituție</li>
                                <li><i class="fas fa-check text-success me-2"></i>Informații complete despre ședințe</li>
                                <li><i class="fas fa-check text-success me-2"></i>Export în format TXT, PDF și Excel</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h6 text-primary">Notă Importantă</h3>
                            <p class="text-muted small mb-0">
                                Datele afișate sunt preluate în timp real de la Portalul Instanțelor de Judecată
                                și pot fi supuse unor întârzieri sau modificări. Pentru informații oficiale,
                                consultați <a href="http://portal.just.ro" target="_blank" rel="noopener">portal.just.ro</a>.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inițializăm loading overlay-ul pentru căutare ședințe
    initSessionLoadingOverlay();

    // Inițializăm navigația mobilă
    initMobileNavigation();

    // Inițializăm validarea formularului
    initFormValidation();

    // Inițializăm calendar picker pentru data ședinței
    initDatePicker();

    // Inițializăm dropdown-ul searchable pentru instituții
    initSearchableInstitutionDropdown();

    // Inițializăm gestionarea link-urilor pentru dosare
    initCaseLinksHandling();

    // Afișăm conținutul principal după încărcare
    setTimeout(function() {
        const mainContent = document.getElementById('sessionMainContent');
        if (mainContent) {
            mainContent.classList.add('loaded');
        }
    }, 100);
});



/**
 * Inițializează loading overlay-ul pentru căutare ședințe
 */
function initSessionLoadingOverlay() {
    const form = document.getElementById('sessionSearchForm');
    const overlay = document.getElementById('sessionLoadingOverlay');

    if (form && overlay) {
        form.addEventListener('submit', function() {
            overlay.style.display = 'flex';

            // Ascundem overlay-ul după maximum 30 secunde
            setTimeout(function() {
                overlay.classList.add('fade-out');
                setTimeout(function() {
                    overlay.style.display = 'none';
                    overlay.classList.remove('fade-out');
                }, 500);
            }, 30000);
        });
    }
}

/**
 * Inițializează navigația mobilă
 */
function initMobileNavigation() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNavigation');

    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            const isOpen = mainNav.classList.contains('mobile-open');

            if (isOpen) {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            } else {
                mainNav.classList.add('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'true');
                mobileToggle.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
            }
        });

        // Închide meniul mobil când se face click pe un link
        const navLinks = mainNav.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            });
        });
    }
}

/**
 * Inițializează validarea formularului
 */
function initFormValidation() {
    const form = document.getElementById('sessionSearchForm');
    const dateInput = document.getElementById('dataSedinta');

    if (form && dateInput) {
        // Validare în timp real pentru data ședinței
        dateInput.addEventListener('input', function() {
            validateDateInput(this);
        });

        // Validare la submit
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                showNotification('Vă rugăm să corectați erorile din formular.', 'danger');
            }
        });
    }
}

/**
 * Validează input-ul pentru dată
 */
function validateDateInput(input) {
    const value = input.value.trim();
    const datePattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;

    // Eliminăm clasele de validare anterioare
    input.classList.remove('is-valid', 'is-invalid');

    if (value === '') {
        return; // Câmpul este obligatoriu, dar nu validăm dacă este gol
    }

    if (!datePattern.test(value)) {
        input.classList.add('is-invalid');
        return false;
    }

    const matches = value.match(datePattern);
    const day = parseInt(matches[1]);
    const month = parseInt(matches[2]);
    const year = parseInt(matches[3]);

    // Verificăm validitatea datei
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
        input.classList.add('is-invalid');
        return false;
    }

    // Verificăm limitele anului
    const currentYear = new Date().getFullYear();
    if (year < 1990 || year > currentYear + 5) {
        input.classList.add('is-invalid');
        return false;
    }

    input.classList.add('is-valid');
    return true;
}

/**
 * Validează întregul formular
 */
function validateForm() {
    const dateInput = document.getElementById('dataSedinta');
    let isValid = true;

    if (dateInput) {
        if (!validateDateInput(dateInput)) {
            isValid = false;
        }

        if (dateInput.value.trim() === '') {
            dateInput.classList.add('is-invalid');
            isValid = false;
        }
    }

    return isValid;
}

/**
 * Inițializează date picker pentru data ședinței
 */
function initDatePicker() {
    const todayBtn = document.getElementById('todayBtn');
    const tomorrowBtn = document.getElementById('tomorrowBtn');
    const dateInput = document.getElementById('dataSedinta');

    if (todayBtn && dateInput) {
        todayBtn.addEventListener('click', function() {
            dateInput.value = formatDateToRomanian(new Date());
            validateDateInput(dateInput);
        });
    }

    if (tomorrowBtn && dateInput) {
        tomorrowBtn.addEventListener('click', function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            dateInput.value = formatDateToRomanian(tomorrow);
            validateDateInput(dateInput);
        });
    }
}

/**
 * Inițializează dropdown-ul searchable pentru instituții
 */
function initSearchableInstitutionDropdown() {
    const searchInput = document.getElementById('institutieSearch');
    const hiddenSelect = document.getElementById('institutie');
    const dropdown = document.getElementById('institutieDropdown');

    if (!searchInput || !hiddenSelect || !dropdown) {
        return;
    }

    // Construim lista de opțiuni din select-ul ascuns
    const options = Array.from(hiddenSelect.options).map(option => ({
        value: option.value,
        text: option.text,
        selected: option.selected
    }));

    // Setăm valoarea inițială dacă există o selecție
    const selectedOption = options.find(opt => opt.selected);
    if (selectedOption && selectedOption.value !== '') {
        searchInput.value = selectedOption.text;
    }

    let highlightedIndex = -1;

    // Funcție pentru afișarea opțiunilor
    function showOptions(filteredOptions = options) {
        dropdown.innerHTML = '';

        if (filteredOptions.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'dropdown-item';
            noResults.textContent = 'Nu au fost găsite rezultate';
            noResults.style.fontStyle = 'italic';
            noResults.style.color = '#6c757d';
            dropdown.appendChild(noResults);
        } else {
            filteredOptions.forEach((option, index) => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.textContent = option.text;
                item.dataset.value = option.value;
                item.dataset.index = index;

                if (option.selected) {
                    item.classList.add('selected');
                }

                item.addEventListener('click', function() {
                    selectOption(option);
                });

                dropdown.appendChild(item);
            });
        }

        dropdown.classList.add('show');
        highlightedIndex = -1;
    }

    // Funcție pentru selectarea unei opțiuni
    function selectOption(option) {
        searchInput.value = option.text;
        hiddenSelect.value = option.value;
        dropdown.classList.remove('show');

        // Actualizăm starea selected în opțiuni
        options.forEach(opt => opt.selected = false);
        option.selected = true;

        // Trigger change event pentru validare
        hiddenSelect.dispatchEvent(new Event('change'));
    }

    // Funcție pentru filtrarea opțiunilor
    function filterOptions(query) {
        const normalizedQuery = normalizeRomanianText(query.toLowerCase());
        return options.filter(option => {
            const normalizedText = normalizeRomanianText(option.text.toLowerCase());
            return normalizedText.includes(normalizedQuery);
        });
    }

    // Funcție pentru normalizarea textului românesc
    function normalizeRomanianText(text) {
        return text
            .replace(/ă/g, 'a')
            .replace(/â/g, 'a')
            .replace(/î/g, 'i')
            .replace(/ș/g, 's')
            .replace(/ț/g, 't');
    }

    // Event listeners pentru input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        if (query === '') {
            showOptions();
        } else {
            const filtered = filterOptions(query);
            showOptions(filtered);
        }
    });

    searchInput.addEventListener('focus', function() {
        const query = this.value.trim();
        if (query === '') {
            showOptions();
        } else {
            const filtered = filterOptions(query);
            showOptions(filtered);
        }
    });

    // Navigare cu tastatura
    searchInput.addEventListener('keydown', function(e) {
        const items = dropdown.querySelectorAll('.dropdown-item:not([style*="italic"])');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
            updateHighlight(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            highlightedIndex = Math.max(highlightedIndex - 1, -1);
            updateHighlight(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (highlightedIndex >= 0 && items[highlightedIndex]) {
                const value = items[highlightedIndex].dataset.value;
                const option = options.find(opt => opt.value === value);
                if (option) {
                    selectOption(option);
                }
            }
        } else if (e.key === 'Escape') {
            dropdown.classList.remove('show');
            highlightedIndex = -1;
        }
    });

    // Funcție pentru actualizarea highlight-ului
    function updateHighlight(items) {
        items.forEach((item, index) => {
            item.classList.toggle('highlighted', index === highlightedIndex);
        });

        // Scroll la elementul highlighted
        if (highlightedIndex >= 0 && items[highlightedIndex]) {
            items[highlightedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }

    // Închide dropdown-ul când se face click în afara lui
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });

    // Reset la schimbarea formularului
    const form = document.getElementById('sessionSearchForm');
    if (form) {
        form.addEventListener('reset', function() {
            setTimeout(() => {
                searchInput.value = '';
                hiddenSelect.value = '';
                options.forEach(opt => opt.selected = opt.value === '');
                dropdown.classList.remove('show');
            }, 10);
        });
    }
}

/**
 * Formatează o dată în format românesc DD.MM.YYYY
 */
function formatDateToRomanian(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
}

/**
 * Resetează formularul
 */
function resetForm() {
    const form = document.getElementById('sessionSearchForm');
    if (form) {
        form.reset();

        // Eliminăm clasele de validare
        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
    }
}

/**
 * Inițializează gestionarea link-urilor pentru dosare
 */
function initCaseLinksHandling() {
    const caseLinks = document.querySelectorAll('.case-link, .case-link-enhanced');

    caseLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Adăugăm un indicator vizual că link-ul a fost accesat
            this.style.opacity = '0.8';

            // Verificăm dacă avem parametrii necesari
            const caseNumber = this.dataset.caseNumber;
            const institution = this.dataset.institution;

            if (!caseNumber) {
                e.preventDefault();
                showNotification('Eroare: Numărul dosarului nu este disponibil.', 'danger');
                this.style.opacity = '1';
                return;
            }

            // Afișăm un mesaj de încărcare
            showNotification(`Se încarcă detaliile dosarului ${caseNumber}...`, 'info');

            // Logăm navigarea pentru debugging
            console.log('Navigare către dosar:', {
                numar: caseNumber,
                institutie: institution,
                url: this.href
            });
        });

        // Restaurăm opacitatea la hover out
        link.addEventListener('mouseleave', function() {
            this.style.opacity = '1';
        });

        // Adăugăm efect de focus pentru accesibilitate
        link.addEventListener('focus', function() {
            this.style.outline = '2px solid #007bff';
            this.style.outlineOffset = '2px';
        });

        link.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });
}

/**
 * Afișează notificări
 */
function showNotification(message, type = 'info') {
    // Implementare simplă de notificare
    const alertClass = type === 'danger' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto-remove după 5 secunde
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
