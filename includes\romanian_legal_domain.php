<?php
/**
 * Romanian Legal Domain Optimization
 * Comprehensive Romanian legal terminology, geographic data, and localization
 * Portal Judiciar România - DosareJust.ro
 */

/**
 * Enhanced Romanian Legal Terminology Database
 * Comprehensive legal terms organized by domain and category
 */
class RomanianLegalTerminology {
    
    /**
     * Get comprehensive Romanian legal terminology by category
     * @return array Organized legal terms
     */
    public static function getLegalTerminology() {
        return [
            'civil_law' => [
                'primary_terms' => [
                    'civil', 'civilă', 'civile', 'drept civil', 'proces civil', 'litigiu civil',
                    'contencios civil', 'acțiune civilă', 'cerere civilă', 'plângere civilă'
                ],
                'contracts' => [
                    'contract', 'contracte', 'contractual', 'contractuală', 'obligații contractuale',
                    'încălcare contract', 'reziliere contract', 'anulare contract', 'executare contract'
                ],
                'property' => [
                    'proprietate', 'drept de proprietate', 'revendicare', 'uzucapiune',
                    'servitute', 'ipotecă', 'gaj', 'drept real', 'posesie', 'detenție'
                ],
                'family' => [
                    'familie', 'căsătorie', 'divorț', 'separație', 'custodie', 'întreținere',
                    'adopție', 'tutelă', 'curatelă', 'autoritate părintească'
                ],
                'damages' => [
                    'daune', 'daune-interese', 'prejudiciu', 'repararea prejudiciului',
                    'răspundere civilă', 'răspundere delictuală', 'răspundere contractuală'
                ]
            ],
            
            'criminal_law' => [
                'primary_terms' => [
                    'penal', 'penală', 'penale', 'drept penal', 'proces penal', 'cauză penală',
                    'dosar penal', 'urmărire penală', 'acțiune penală', 'plângere penală'
                ],
                'crimes' => [
                    'infracțiune', 'infracțiuni', 'crimă', 'crime', 'delict', 'delicte',
                    'contravenție', 'contravenții', 'faptă penală', 'act criminal'
                ],
                'procedures' => [
                    'cercetare penală', 'urmărire penală', 'trimitere în judecată',
                    'rechizitoriu', 'ordonanță', 'sentință penală', 'hotărâre penală'
                ],
                'penalties' => [
                    'pedeapsă', 'pedepse', 'închisoare', 'amendă', 'muncă în folosul comunității',
                    'suspendare', 'probațiune', 'libertate condiționată'
                ]
            ],
            
            'administrative_law' => [
                'primary_terms' => [
                    'administrativ', 'administrativă', 'administrative', 'drept administrativ',
                    'contencios administrativ', 'litigiu administrativ', 'proces administrativ'
                ],
                'authorities' => [
                    'autoritate publică', 'autoritate administrativă', 'instituție publică',
                    'organ administrativ', 'administrație publică', 'serviciu public'
                ],
                'acts' => [
                    'act administrativ', 'decizie administrativă', 'ordin', 'ordonanță',
                    'regulament', 'instrucțiune', 'circulară', 'dispoziție'
                ],
                'procedures' => [
                    'procedură administrativă', 'recurs administrativ', 'plângere administrativă',
                    'contestație administrativă', 'anulare act administrativ'
                ]
            ],
            
            'commercial_law' => [
                'primary_terms' => [
                    'comercial', 'comercială', 'comerciale', 'drept comercial', 'societate comercială',
                    'întreprindere', 'afaceri', 'comerț', 'activitate comercială'
                ],
                'companies' => [
                    'societate pe acțiuni', 'societate cu răspundere limitată', 'SRL', 'SA',
                    'întreprindere individuală', 'PFA', 'asociație', 'fundație'
                ],
                'procedures' => [
                    'înregistrare comercială', 'registrul comerțului', 'faliment', 'insolvență',
                    'reorganizare judiciară', 'lichidare', 'concordat preventiv'
                ]
            ],
            
            'labor_law' => [
                'primary_terms' => [
                    'muncă', 'munca', 'dreptul muncii', 'relații de muncă', 'contract de muncă',
                    'angajat', 'angajator', 'salariat', 'lucrător'
                ],
                'disputes' => [
                    'conflict de muncă', 'litigiu de muncă', 'concediere', 'desfacere contract',
                    'discriminare', 'hărțuire', 'accident de muncă', 'boală profesională'
                ],
                'rights' => [
                    'salariu', 'indemnizație', 'concediu', 'odihnă', 'program de lucru',
                    'ore suplimentare', 'sindicate', 'negociere colectivă'
                ]
            ],
            
            'fiscal_law' => [
                'primary_terms' => [
                    'fiscal', 'fiscală', 'fiscale', 'drept fiscal', 'contencios fiscal',
                    'impozit', 'taxă', 'contribuție', 'obligație fiscală'
                ],
                'procedures' => [
                    'control fiscal', 'inspecție fiscală', 'decizie de impunere',
                    'contestație fiscală', 'executare silită', 'poprire'
                ],
                'entities' => [
                    'ANAF', 'Agenția Națională de Administrare Fiscală', 'fisc',
                    'administrația fiscală', 'contribuabil', 'plătitor'
                ]
            ]
        ];
    }
    
    /**
     * Get Romanian court hierarchy and jurisdiction data
     * @return array Court hierarchy information
     */
    public static function getCourtHierarchy() {
        return [
            'supreme_court' => [
                'name' => 'Înalta Curte de Casație și Justiție',
                'code' => 'InaltaCurtedeCASSATIESIJUSTITIE',
                'level' => 4,
                'jurisdiction' => 'național',
                'location' => 'București',
                'type' => 'curte_suprema',
                'description' => 'Instanța supremă a României, cu competență în casație și în soluționarea recursurilor în interesul legii'
            ],
            
            'appeal_courts' => [
                'CurteadeApelBUCURESTI' => [
                    'name' => 'Curtea de Apel București',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'București',
                    'counties' => ['București', 'Ilfov', 'Călărași', 'Giurgiu', 'Ialomița', 'Teleorman'],
                    'type' => 'curte_apel'
                ],
                'CurteadeApelCLUJ' => [
                    'name' => 'Curtea de Apel Cluj',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'Cluj-Napoca',
                    'counties' => ['Cluj', 'Bistrița-Năsăud', 'Maramureș', 'Sălaj'],
                    'type' => 'curte_apel'
                ],
                'CurteadeApelCONSTANTA' => [
                    'name' => 'Curtea de Apel Constanța',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'Constanța',
                    'counties' => ['Constanța', 'Tulcea'],
                    'type' => 'curte_apel'
                ],
                'CurteadeApelCRAIOVA' => [
                    'name' => 'Curtea de Apel Craiova',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'Craiova',
                    'counties' => ['Dolj', 'Gorj', 'Mehedinți', 'Olt', 'Vâlcea'],
                    'type' => 'curte_apel'
                ],
                'CurteadeApelIASI' => [
                    'name' => 'Curtea de Apel Iași',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'Iași',
                    'counties' => ['Iași', 'Botoșani', 'Neamț', 'Suceava', 'Vaslui'],
                    'type' => 'curte_apel'
                ],
                'CurteadeApelTIMISOARA' => [
                    'name' => 'Curtea de Apel Timișoara',
                    'level' => 3,
                    'jurisdiction' => 'regional',
                    'location' => 'Timișoara',
                    'counties' => ['Timiș', 'Arad', 'Caraș-Severin', 'Hunedoara'],
                    'type' => 'curte_apel'
                ]
            ]
        ];
    }
    
    /**
     * Get Romanian geographic and administrative data
     * @return array Geographic information
     */
    public static function getGeographicData() {
        return [
            'regions' => [
                'moldova' => [
                    'name' => 'Moldova',
                    'counties' => ['Bacău', 'Botoșani', 'Iași', 'Neamț', 'Suceava', 'Vaslui'],
                    'appeal_court' => 'CurteadeApelIASI'
                ],
                'muntenia' => [
                    'name' => 'Muntenia',
                    'counties' => ['Argeș', 'Buzău', 'Dâmbovița', 'Prahova', 'Teleorman', 'Călărași', 'Giurgiu', 'Ialomița'],
                    'appeal_court' => 'CurteadeApelBUCURESTI'
                ],
                'oltenia' => [
                    'name' => 'Oltenia',
                    'counties' => ['Dolj', 'Gorj', 'Mehedinți', 'Olt', 'Vâlcea'],
                    'appeal_court' => 'CurteadeApelCRAIOVA'
                ],
                'transilvania' => [
                    'name' => 'Transilvania',
                    'counties' => ['Alba', 'Brașov', 'Cluj', 'Covasna', 'Harghita', 'Hunedoara', 'Mureș', 'Sibiu'],
                    'appeal_court' => 'CurteadeApelCLUJ'
                ],
                'banat' => [
                    'name' => 'Banat',
                    'counties' => ['Arad', 'Caraș-Severin', 'Timiș'],
                    'appeal_court' => 'CurteadeApelTIMISOARA'
                ],
                'dobrogea' => [
                    'name' => 'Dobrogea',
                    'counties' => ['Constanța', 'Tulcea'],
                    'appeal_court' => 'CurteadeApelCONSTANTA'
                ]
            ],
            
            'major_cities' => [
                'București' => ['county' => 'București', 'region' => 'muntenia', 'population' => 1883425],
                'Cluj-Napoca' => ['county' => 'Cluj', 'region' => 'transilvania', 'population' => 324576],
                'Timișoara' => ['county' => 'Timiș', 'region' => 'banat', 'population' => 319279],
                'Iași' => ['county' => 'Iași', 'region' => 'moldova', 'population' => 290422],
                'Constanța' => ['county' => 'Constanța', 'region' => 'dobrogea', 'population' => 283872],
                'Craiova' => ['county' => 'Dolj', 'region' => 'oltenia', 'population' => 269506],
                'Brașov' => ['county' => 'Brașov', 'region' => 'transilvania', 'population' => 253200],
                'Galați' => ['county' => 'Galați', 'region' => 'moldova', 'population' => 249432]
            ]
        ];
    }
}
