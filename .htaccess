# =============================================================================
# Portal Judiciar România - Advanced Performance & Security Configuration
# DosareJust.ro - Technical SEO Infrastructure
# =============================================================================

# Enable Rewrite Engine
RewriteEngine On

# Set Base Directory
RewriteBase /

# =============================================================================
# SECURITY HEADERS - Modern Web Security Standards
# =============================================================================

<IfModule mod_headers.c>
    # Content Security Policy - Prevent XSS attacks
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://code.jquery.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'self'; base-uri 'self'; form-action 'self';"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Clickjacking Protection
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Referrer Policy for Privacy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Strict Transport Security (HTTPS enforcement)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Permissions Policy (Feature Policy)
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()"
    
    # Remove Server Information
    Header always unset Server
    Header always unset X-Powered-By
    
    # SEO and Performance Headers
    Header always set X-UA-Compatible "IE=edge"
    Header always set Vary "Accept-Encoding, User-Agent"
</IfModule>

# =============================================================================
# PERFORMANCE OPTIMIZATION - Advanced Caching & Compression
# =============================================================================

# Enable Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE application/json
    
    # Remove browser bugs (only needed for really old browsers)
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
    Header append Vary User-Agent
</IfModule>

# Advanced Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images - 1 year
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    
    # Fonts - 1 year
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType application/x-font-woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    
    # CSS and JavaScript - 1 month
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # HTML and XML - 1 hour
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
    ExpiresByType application/xhtml+xml "access plus 1 hour"
    
    # JSON and RSS - 1 hour
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/rss+xml "access plus 1 hour"
    
    # Documents - 1 month
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    
    # Default - 1 day
    ExpiresDefault "access plus 1 day"
</IfModule>

# =============================================================================
# URL REWRITING - SEO-Friendly URLs
# =============================================================================

# Remove trailing slashes (SEO best practice)
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{THE_REQUEST} /+[^\s]*\?[^\s]* [OR]
RewriteCond %{THE_REQUEST} /+[^\s]*/$
RewriteRule ^(.*)$ /$1? [R=301,L]

# Redirect to PHP files without extension
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [L]

# =============================================================================
# DIRECTORY PROTECTION & FILE ACCESS
# =============================================================================

# Disable directory browsing
Options -Indexes

# Set default index
DirectoryIndex index.php index.html

# Set default charset
AddDefaultCharset UTF-8

# MIME Types for better performance
AddType application/javascript .js
AddType text/css .css
AddType application/font-woff .woff
AddType application/font-woff2 .woff2

# =============================================================================
# SECURITY - File Protection
# =============================================================================

# Protect sensitive files - Apache 2.4+ syntax
<FilesMatch "^\.ht">
    Require all denied
</FilesMatch>

# Protect configuration files
<FilesMatch "\.(env|ini|log|sh|sql|conf|config)$">
    Require all denied
</FilesMatch>

# Protect PHP includes
<FilesMatch "^(includes|services|vendor|config)/">
    Require all denied
</FilesMatch>

# =============================================================================
# ERROR PAGES - SEO-Optimized Custom Error Pages
# =============================================================================

ErrorDocument 404 /404.php
ErrorDocument 500 /500.php
ErrorDocument 403 /403.php

# =============================================================================
# ROMANIAN LEGAL PORTAL SPECIFIC OPTIMIZATIONS
# =============================================================================

# Force HTTPS for production (uncomment when SSL is available)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Canonical domain enforcement (uncomment and adjust for production)
# RewriteCond %{HTTP_HOST} !^dosare\.just\.ro$ [NC]
# RewriteRule ^(.*)$ https://dosare.just.ro/$1 [L,R=301]
