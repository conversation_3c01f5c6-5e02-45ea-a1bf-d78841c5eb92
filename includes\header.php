<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <?php
    // SEO Meta Tags - Dynamic based on page
    $page_title = isset($seo_title) ? $seo_title : APP_NAME;
    $page_description = isset($seo_description) ? $seo_description : "Portal oficial pentru căutarea dosarelor judecătorești din România. Acces rapid la informații despre dosare civile, penale și comerciale din toate instanțele românești.";
    $page_keywords = isset($seo_keywords) ? $seo_keywords : "dosare judecătorești, portal judiciar România, căutare dosare online, instanțe România, tribunal, curtea de apel, înalta curte, dosare civile, dosare penale";
    $page_url = isset($canonical_url) ? $canonical_url : "http" . (isset($_SERVER['HTTPS']) ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    $page_image = isset($og_image) ? $og_image : "http" . (isset($_SERVER['HTTPS']) ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . "/images/logo.jpg";
    ?>

    <!-- Primary Meta Tags -->
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_keywords); ?>">
    <meta name="author" content="Portal Dosare Judecătorești România">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo htmlspecialchars($page_url); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlspecialchars($page_url); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($page_image); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="ro_RO">
    <meta property="og:site_name" content="Portal Dosare Judecătorești România">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo htmlspecialchars($page_url); ?>">
    <meta property="twitter:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="twitter:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="twitter:image" content="<?php echo htmlspecialchars($page_image); ?>">

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="geo.region" content="RO">
    <meta name="geo.country" content="Romania">
    <meta name="language" content="Romanian">
    <meta name="distribution" content="global">
    <meta name="rating" content="general">

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "Organization",
                "@id": "<?php echo htmlspecialchars($page_url); ?>#organization",
                "name": "Portal Dosare Judecătorești România",
                "alternateName": "DosareJust.ro",
                "url": "<?php echo htmlspecialchars($page_url); ?>",
                "logo": {
                    "@type": "ImageObject",
                    "url": "<?php echo htmlspecialchars($page_image); ?>",
                    "width": 400,
                    "height": 400
                },
                "description": "Portal oficial pentru căutarea și consultarea dosarelor judecătorești din România",
                "address": {
                    "@type": "PostalAddress",
                    "addressCountry": "RO",
                    "addressRegion": "România"
                },
                "contactPoint": {
                    "@type": "ContactPoint",
                    "contactType": "customer service",
                    "availableLanguage": "Romanian"
                }
            },
            {
                "@type": "WebSite",
                "@id": "<?php echo htmlspecialchars($page_url); ?>#website",
                "url": "<?php echo htmlspecialchars($page_url); ?>",
                "name": "Portal Dosare Judecătorești România",
                "description": "Portal oficial pentru căutarea dosarelor judecătorești din România",
                "publisher": {
                    "@id": "<?php echo htmlspecialchars($page_url); ?>#organization"
                },
                "inLanguage": "ro-RO",
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": {
                        "@type": "EntryPoint",
                        "urlTemplate": "<?php echo htmlspecialchars($page_url); ?>search.php?q={search_term_string}"
                    },
                    "query-input": "required name=search_term_string"
                }
            },
            {
                "@type": "GovernmentService",
                "name": "Serviciu de Căutare Dosare Judecătorești",
                "description": "Serviciu public pentru căutarea și consultarea dosarelor judecătorești din România",
                "provider": {
                    "@id": "<?php echo htmlspecialchars($page_url); ?>#organization"
                },
                "areaServed": {
                    "@type": "Country",
                    "name": "România"
                },
                "availableChannel": {
                    "@type": "ServiceChannel",
                    "serviceUrl": "<?php echo htmlspecialchars($page_url); ?>",
                    "serviceType": "Online"
                }
            }
        ]
    }
    </script>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/buttons.css"><!-- Stiluri specifice pentru butoane -->
    <link rel="stylesheet" href="assets/css/footer.css"><!-- Stiluri moderne pentru footer -->

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a class="sr-only sr-only-focusable" href="#main-content">Sari la conținutul principal</a>

    <!-- Main Header -->
    <header role="banner" class="site-header">
        <!-- Navigation will be added by individual pages -->
    </header>

    <!-- Main Content Area -->
    <main id="main-content" role="main" class="main-content">