<?php
/**
 * Enhanced Header template pentru Portal Judiciar România
 * Include Romanian Legal Domain Optimization, meta tags SEO, stiluri CSS și scripturi JavaScript
 */

// Include Romanian legal domain optimization files
require_once __DIR__ . '/romanian_legal_domain.php';
require_once __DIR__ . '/romanian_structured_data.php';
require_once __DIR__ . '/romanian_localization.php';

// Determine page type for enhanced SEO
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$page_type = 'homepage';
switch ($current_page) {
    case 'sedinte':
        $page_type = 'sessions';
        break;
    case 'detalii_dosar':
        $page_type = 'details';
        break;
    case 'index':
        $page_type = 'homepage';
        break;
    default:
        $page_type = 'search';
}

// Enhanced SEO context
$seo_context = [
    'page_type' => $page_type,
    'institution' => $_GET['institutie'] ?? '',
    'location' => '',
    'case_type' => $_GET['categorieCaz'] ?? ''
];

// Verificăm dacă variabilele SEO sunt definite, altfel setăm valori implicite optimizate
if (!isset($seo_title)) {
    $seo_title = "Portal Judiciar România - Căutare Dosare Online | DosareJust.ro";
}
if (!isset($seo_description)) {
    $seo_description = RomanianLocalization::generateLegalMetaDescription($page_type, $seo_context);
}
if (!isset($seo_keywords)) {
    $seo_keywords = RomanianLocalization::generateLegalKeywords($page_type, $seo_context);
}

// Generate enhanced structured data
$structured_data_context = [
    'breadcrumb_items' => $breadcrumb_items ?? []
];
if (isset($_GET['institutie']) && !empty($_GET['institutie'])) {
    $structured_data_context['court_code'] = $_GET['institutie'];
}
$structured_data = RomanianStructuredData::generatePageStructuredData($page_type, $structured_data_context);
?>
<!DOCTYPE html>
<html lang="ro-RO">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- Enhanced SEO Meta Tags -->
    <title><?php echo htmlspecialchars($seo_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($seo_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($seo_keywords); ?>">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="language" content="Romanian">
    <meta name="geo.region" content="RO">
    <meta name="geo.country" content="Romania">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://<?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="<?php echo htmlspecialchars($seo_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($seo_description); ?>">
    <meta property="og:url" content="https://<?php echo $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:site_name" content="Portal Judiciar România - DosareJust.ro">
    <meta property="og:locale" content="ro_RO">
    <meta property="og:image" content="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/images/portal-preview.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Portal Judiciar România - Căutare Dosare Online">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($seo_title); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($seo_description); ?>">
    <meta name="twitter:image" content="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/images/portal-preview.png">
    <meta name="twitter:image:alt" content="Portal Judiciar România - Căutare Dosare Online">

    <!-- Enhanced Structured Data -->
    <script type="application/ld+json">
    <?php echo $structured_data; ?>
    </script>

    <!-- PWA and Mobile Optimization -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="DosareJust">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Web App Manifest for PWA -->
    <link rel="manifest" href="/manifest.json">

    <!-- Favicons and Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/images/favicon-16x16.png">


    <!-- Performance Optimization - Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://code.jquery.com">

    <!-- CSS Libraries with Performance Optimization -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" integrity="sha384-B0vP5xmATw1+K9KRQjQERJvTumQW0nPEzvF6L/Z6nronJ3oUOFUFpCjEUQouq2+l" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous">

    <!-- Romanian Legal Portal Custom Styles -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/buttons.css">
    <link rel="stylesheet" href="assets/css/footer.css">

    <!-- Romanian Typography and Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Flatpickr for Romanian Date Formatting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://npmcdn.com/flatpickr/dist/themes/material_blue.css">

    <!-- JavaScript Libraries with Performance Optimization -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha384-vtXRMe3mGCbOeY7l30aIg8H9p3GdeSe4IFlP6G8JMa7o7lXvnz3GFKzPxzJdPfGK" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-Piv4xVNRyMGpqkS2by6br4gNJ7DXjqk09RmUpJ8jgGtD7zP9yug3goQfGII0yAns" crossorigin="anonymous"></script>

    <!-- Romanian Date Picker -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://npmcdn.com/flatpickr/dist/l10n/ro.js"></script>

    <!-- Service Worker Registration for Performance -->
    <script>
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('Service Worker registered successfully:', registration.scope);

                    // Check for updates
                    registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New content is available, show update notification
                                if (confirm('O versiune nouă a portalului este disponibilă. Doriți să actualizați?')) {
                                    window.location.reload();
                                }
                            }
                        });
                    });
                })
                .catch(function(error) {
                    console.log('Service Worker registration failed:', error);
                });
        });
    }
    </script>

    <!-- Romanian Legal Portal Analytics and Performance -->
    <script>
    // Performance monitoring for Romanian legal portal
    window.addEventListener('load', function() {
        // Log page load performance
        const perfData = performance.getEntriesByType('navigation')[0];
        if (perfData) {
            console.log('Portal Performance:', {
                'Timp încărcare': Math.round(perfData.loadEventEnd - perfData.fetchStart) + 'ms',
                'Timp până la primul byte': Math.round(perfData.responseStart - perfData.fetchStart) + 'ms',
                'Timp procesare DOM': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart) + 'ms'
            });
        }
    });
    </script>
</head>
<body class="romanian-legal-portal">
    <!-- Romanian Legal Portal Header -->
    <header class="portal-header" role="banner">
        <div class="container-fluid">
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary" role="navigation" aria-label="Navigare principală Portal Judiciar România">
                <a class="navbar-brand" href="/index.php" title="Portal Judiciar România - Acasă">
                    <i class="fas fa-gavel mr-2" aria-hidden="true"></i>
                    <span>Portal Judiciar România</span>
                </a>

                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Comutare navigare">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/index.php" title="Căutare dosare judecătorești">
                                <i class="fas fa-search mr-1" aria-hidden="true"></i>
                                Căutare Dosare
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/sedinte.php" title="Ședințe judecată programate">
                                <i class="fas fa-calendar-alt mr-1" aria-hidden="true"></i>
                                Ședințe Judecată
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/health.php" title="Starea sistemului">
                                <i class="fas fa-heartbeat mr-1" aria-hidden="true"></i>
                                Stare Sistem
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content Area -->
    <main role="main" class="main-content"><?php
    // Breadcrumb navigation if items are provided
    if (isset($breadcrumb_items) && !empty($breadcrumb_items)): ?>
        <nav aria-label="Navigare ierarhică" class="breadcrumb-nav">
            <div class="container">
                <ol class="breadcrumb bg-light">
                    <?php foreach ($breadcrumb_items as $index => $item): ?>
                        <?php if ($index === count($breadcrumb_items) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($item['name']); ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo htmlspecialchars($item['url']); ?>" title="<?php echo htmlspecialchars($item['name']); ?>">
                                    <?php echo htmlspecialchars($item['name']); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </div>
        </nav>
    <?php endif; ?>