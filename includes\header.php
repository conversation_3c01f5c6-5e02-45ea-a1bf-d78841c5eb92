<?php
// SEO Meta Tags - Dynamic based on page
$page_title = isset($seo_title) ? $seo_title : APP_NAME;
$page_description = isset($seo_description) ? $seo_description : "Portal oficial pentru căutarea dosarelor judecătorești din România. Acces rapid la informații despre procese judiciare, părți implicate și stadiul dosarelor din toate instanțele românești.";
$page_keywords = isset($seo_keywords) ? $seo_keywords : "portal judiciar România, căutare dosare, dosare judecătorești, instanțe România, tribunal, curtea de apel";

// Current page URL for canonical and social sharing
$current_url = "https://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
$page_url = strtok($current_url, '?'); // Remove query parameters for canonical
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">

    <!-- SEO Meta Tags -->
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_keywords); ?>">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="author" content="Portal Dosare Judecătorești România">
    <link rel="canonical" href="<?php echo htmlspecialchars($page_url); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlspecialchars($page_url); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:site_name" content="DosareJust.ro">
    <meta property="og:locale" content="ro_RO">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo htmlspecialchars($page_url); ?>">
    <meta property="twitter:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="twitter:description" content="<?php echo htmlspecialchars($page_description); ?>">

    <!-- Technical Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "Organization",
                "name": "Portal Dosare Judecătorești România",
                "url": "<?php echo htmlspecialchars($page_url); ?>",
                "description": "Portal oficial pentru căutarea și consultarea dosarelor judecătorești din România",
                "address": {
                    "@type": "PostalAddress",
                    "addressCountry": "RO"
                }
            },
            {
                "@type": "WebSite",
                "url": "<?php echo htmlspecialchars($page_url); ?>",
                "name": "DosareJust.ro",
                "description": "Portal pentru căutarea dosarelor judecătorești din România",
                "inLanguage": "ro",
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": "<?php echo htmlspecialchars(dirname($page_url)); ?>/index.php?search={search_term_string}",
                    "query-input": "required name=search_term_string"
                }
            },
            {
                "@type": "GovernmentService",
                "name": "Căutare Dosare Judecătorești",
                "description": "Serviciu de căutare online pentru dosarele judecătorești din România",
                "provider": {
                    "@type": "Organization",
                    "name": "Portal Dosare Judecătorești România"
                },
                "areaServed": {
                    "@type": "Country",
                    "name": "România"
                },
                "serviceType": "Căutare informații juridice publice"
            }
        ]
    }
    </script>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/buttons.css"><!-- Stiluri specifice pentru butoane -->
    <link rel="stylesheet" href="assets/css/footer.css"><!-- Stiluri moderne pentru footer -->

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only sr-only-focusable">Sari la conținutul principal</a>

    <header role="banner">