<?php
/**
 * Comprehensive Debug Script for Romanian Legal Portal
 * This script helps diagnose issues with the portal after Romanian Legal Domain Optimization
 */

// Enable comprehensive error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ro'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Portal Debug - Romanian Legal Domain</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 Portal Judiciar România - Debug Report</h1>";
echo "<p>Generated: " . date('Y-m-d H:i:s') . "</p>";

// 1. PHP Environment Check
echo "<h2>1. 📋 PHP Environment</h2>";
echo "<div class='info'>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Path: " . __FILE__ . "<br>";
echo "</div>";

// 2. File Existence Check
echo "<h2>2. 📁 File Existence Check</h2>";
$required_files = [
    'includes/config.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/romanian_legal_domain.php',
    'includes/romanian_structured_data.php',
    'includes/romanian_localization.php',
    'services/DosarService.php',
    'services/SedinteService.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ {$file}</span><br>";
    } else {
        echo "<span class='error'>❌ {$file} - MISSING</span><br>";
    }
}

// 3. Include Test
echo "<h2>3. 🔗 Include Test</h2>";
$include_tests = [
    'includes/config.php' => 'Config loaded',
    'includes/functions.php' => 'Functions loaded',
    'includes/romanian_legal_domain.php' => 'Romanian Legal Domain loaded',
    'includes/romanian_structured_data.php' => 'Romanian Structured Data loaded',
    'includes/romanian_localization.php' => 'Romanian Localization loaded'
];

foreach ($include_tests as $file => $description) {
    try {
        if (file_exists($file)) {
            require_once $file;
            echo "<span class='success'>✅ {$description}</span><br>";
        } else {
            echo "<span class='error'>❌ {$file} not found</span><br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ {$description} - Error: " . $e->getMessage() . "</span><br>";
    } catch (ParseError $e) {
        echo "<span class='error'>❌ {$description} - Parse Error: " . $e->getMessage() . "</span><br>";
    } catch (Error $e) {
        echo "<span class='error'>❌ {$description} - Fatal Error: " . $e->getMessage() . "</span><br>";
    }
}

// 4. Class Existence Check
echo "<h2>4. 🏗️ Class Existence Check</h2>";
$required_classes = [
    'RomanianLegalTerminology',
    'RomanianCourtHierarchy', 
    'RomanianGeographicData',
    'RomanianStructuredData',
    'RomanianLocalization'
];

foreach ($required_classes as $class) {
    if (class_exists($class)) {
        echo "<span class='success'>✅ Class {$class} exists</span><br>";
    } else {
        echo "<span class='error'>❌ Class {$class} not found</span><br>";
    }
}

// 5. Function Test
echo "<h2>5. ⚙️ Function Test</h2>";
try {
    if (function_exists('getInstanteList')) {
        $instante = getInstanteList();
        echo "<span class='success'>✅ getInstanteList() works - " . count($instante) . " institutions</span><br>";
    } else {
        echo "<span class='error'>❌ getInstanteList() function not found</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ getInstanteList() error: " . $e->getMessage() . "</span><br>";
}

// 6. Romanian Legal Domain Test
echo "<h2>6. 🇷🇴 Romanian Legal Domain Test</h2>";
try {
    if (class_exists('RomanianLocalization')) {
        $seo_desc = RomanianLocalization::generateLegalMetaDescription('homepage');
        echo "<span class='success'>✅ SEO Description generated: " . substr($seo_desc, 0, 100) . "...</span><br>";
        
        $seo_keywords = RomanianLocalization::generateLegalKeywords('homepage');
        echo "<span class='success'>✅ SEO Keywords generated: " . count(explode(', ', $seo_keywords)) . " keywords</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Romanian Localization error: " . $e->getMessage() . "</span><br>";
}

try {
    if (class_exists('RomanianStructuredData')) {
        $structured_data = RomanianStructuredData::generatePageStructuredData('homepage');
        echo "<span class='success'>✅ Structured Data generated: " . strlen($structured_data) . " characters</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ Romanian Structured Data error: " . $e->getMessage() . "</span><br>";
}

// 7. Apache Module Check
echo "<h2>7. 🌐 Apache Module Check</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $required_modules = ['mod_rewrite', 'mod_headers', 'mod_deflate', 'mod_expires'];
    
    foreach ($required_modules as $module) {
        if (in_array($module, $modules)) {
            echo "<span class='success'>✅ {$module} enabled</span><br>";
        } else {
            echo "<span class='warning'>⚠️ {$module} not detected</span><br>";
        }
    }
} else {
    echo "<span class='info'>ℹ️ Apache module check not available (running under CGI/FastCGI)</span><br>";
}

// 8. Error Log Check
echo "<h2>8. 📝 Recent Error Log</h2>";
$error_log_paths = [
    'C:\wamp64\logs\apache_error.log',
    'C:\wamp64\logs\php_error.log',
    ini_get('error_log')
];

foreach ($error_log_paths as $log_path) {
    if ($log_path && file_exists($log_path)) {
        echo "<h3>Log: {$log_path}</h3>";
        $lines = file($log_path);
        $recent_lines = array_slice($lines, -10); // Last 10 lines
        echo "<pre>";
        foreach ($recent_lines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
        break;
    }
}

// 9. Recommendations
echo "<h2>9. 💡 Recommendations</h2>";
echo "<div class='info'>";
echo "<strong>If you see errors above:</strong><br>";
echo "1. Check that all Romanian legal domain files are properly uploaded<br>";
echo "2. Verify file permissions (should be readable by web server)<br>";
echo "3. Check Apache error logs for detailed error messages<br>";
echo "4. Ensure Apache 2.4+ syntax is used in .htaccess files<br>";
echo "5. Verify that required Apache modules are enabled<br>";
echo "<br>";
echo "<strong>If all tests pass:</strong><br>";
echo "✅ Romanian Legal Domain Optimization is working correctly!<br>";
echo "You can now access the portal pages safely.<br>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
