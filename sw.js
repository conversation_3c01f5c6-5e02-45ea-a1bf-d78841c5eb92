/**
 * Service Worker for Portal Judiciar România
 * Performance Optimization & Offline Functionality
 * DosareJust.ro - Technical Infrastructure
 */

const CACHE_NAME = 'portal-judiciar-v1.0.2';
const STATIC_CACHE = 'portal-static-v1.0.2';
const DYNAMIC_CACHE = 'portal-dynamic-v1.0.2';

// Static assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/index.php',
    '/sedinte.php',
    '/assets/css/style.css',
    '/assets/css/responsive.css',
    '/assets/css/buttons.css',
    '/assets/css/footer.css',
    '/assets/js/main.js',
    '/404.php',
    '/500.php',
    // External CDN resources
    'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
    'https://code.jquery.com/jquery-3.6.0.min.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js'
];

// Dynamic content patterns to cache
const DYNAMIC_PATTERNS = [
    /\/detalii_dosar\.php/,
    /\/search\.php/,
    /\/health\.php/
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('[SW] Installing Service Worker for Portal Judiciar România');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('[SW] Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('[SW] Static assets cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('[SW] Error caching static assets:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[SW] Activating Service Worker');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('[SW] Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('[SW] Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external domains (except CDNs)
    if (url.origin !== location.origin && !isCDNResource(url)) {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(request.url)) {
        // Static assets: Cache First strategy
        event.respondWith(cacheFirst(request));
    } else if (isDynamicContent(request.url)) {
        // Dynamic content: Network First strategy
        event.respondWith(networkFirst(request));
    } else if (isAPIRequest(request.url)) {
        // API requests: Network Only (no caching for SOAP calls)
        event.respondWith(networkOnly(request));
    } else {
        // Default: Stale While Revalidate
        event.respondWith(staleWhileRevalidate(request));
    }
});

// Cache First Strategy - for static assets
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            console.log('[SW] Serving from cache:', request.url);
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
            console.log('[SW] Cached new static asset:', request.url);
        }
        return networkResponse;
    } catch (error) {
        console.error('[SW] Cache First error:', error);
        return await caches.match('/404.php') || new Response('Offline', { status: 503 });
    }
}

// Network First Strategy - for dynamic content
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
            console.log('[SW] Updated dynamic cache:', request.url);
        }
        return networkResponse;
    } catch (error) {
        console.log('[SW] Network failed, trying cache:', request.url);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return appropriate offline page
        if (request.url.includes('sedinte.php')) {
            return await caches.match('/sedinte.php') || new Response('Offline - Ședințe', { status: 503 });
        }
        return await caches.match('/404.php') || new Response('Offline', { status: 503 });
    }
}

// Network Only Strategy - for API calls
async function networkOnly(request) {
    try {
        return await fetch(request);
    } catch (error) {
        console.error('[SW] API request failed:', error);
        return new Response(JSON.stringify({
            error: 'Serviciul nu este disponibil momentan',
            message: 'Vă rugăm să încercați din nou mai târziu',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Stale While Revalidate Strategy - for general content
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.error('[SW] Stale while revalidate error:', error);
        return cachedResponse;
    });
    
    return cachedResponse || fetchPromise;
}

// Helper functions
function isStaticAsset(url) {
    return url.includes('/assets/') || 
           url.includes('.css') || 
           url.includes('.js') || 
           url.includes('.png') || 
           url.includes('.jpg') || 
           url.includes('.jpeg') || 
           url.includes('.gif') || 
           url.includes('.svg') || 
           url.includes('.woff') || 
           url.includes('.woff2');
}

function isDynamicContent(url) {
    return DYNAMIC_PATTERNS.some(pattern => pattern.test(url));
}

function isAPIRequest(url) {
    return url.includes('export=') || 
           url.includes('ajax=') || 
           url.includes('api/') ||
           url.includes('.asmx');
}

function isCDNResource(url) {
    const cdnDomains = [
        'cdn.jsdelivr.net',
        'cdnjs.cloudflare.com',
        'code.jquery.com',
        'fonts.googleapis.com',
        'fonts.gstatic.com'
    ];
    return cdnDomains.some(domain => url.hostname.includes(domain));
}

// Background sync for offline form submissions
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync-search') {
        console.log('[SW] Background sync triggered for search');
        event.waitUntil(handleBackgroundSync());
    }
});

async function handleBackgroundSync() {
    // Handle offline search submissions when connection is restored
    try {
        const cache = await caches.open('offline-requests');
        const requests = await cache.keys();
        
        for (const request of requests) {
            try {
                await fetch(request);
                await cache.delete(request);
                console.log('[SW] Offline request synced:', request.url);
            } catch (error) {
                console.error('[SW] Failed to sync request:', error);
            }
        }
    } catch (error) {
        console.error('[SW] Background sync error:', error);
    }
}

// Push notification handling (for future use)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body || 'Notificare de la Portal Judiciar România',
            icon: '/assets/images/icon-192.png',
            badge: '/assets/images/badge-72.png',
            tag: 'portal-notification',
            requireInteraction: true,
            actions: [
                {
                    action: 'view',
                    title: 'Vizualizează',
                    icon: '/assets/images/view-icon.png'
                },
                {
                    action: 'dismiss',
                    title: 'Închide',
                    icon: '/assets/images/close-icon.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'Portal Judiciar România', options)
        );
    }
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

console.log('[SW] Service Worker for Portal Judiciar România loaded successfully');
