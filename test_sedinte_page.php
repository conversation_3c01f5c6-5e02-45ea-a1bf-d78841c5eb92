<?php
// Simulăm o cerere POST către sedinte.php cu CurteadeApelALBAIULIA
$_POST = [
    'dataSedinta' => '15.01.2024',
    'institutie' => 'CurteadeApelALBAIULIA',
    'search' => '1'
];

// Capturăm output-ul
ob_start();

// Includem pagina sedinte.php
include 'sedinte.php';

$output = ob_get_clean();

// Verificăm dacă există erori în output
if (strpos($output, 'Eroare la căutarea ședințelor') !== false) {
    echo "❌ EROARE GĂSITĂ în pagina sedinte.php:\n";
    // Extragem mesajul de eroare
    if (preg_match('/Eroare la căutarea ședințelor: ([^<]+)/', $output, $matches)) {
        echo "Mesaj eroare: " . trim($matches[1]) . "\n";
    }
} else if (strpos($output, 'ședințe găsite') !== false) {
    echo "✅ SUCCESS: Pagina sedinte.php funcționează cu CurteadeApelALBAIULIA\n";
    // Extragem numărul de rezultate
    if (preg_match('/(\d+) ședințe găsite/', $output, $matches)) {
        echo "Numărul de ședințe găsite: " . $matches[1] . "\n";
    }
} else {
    echo "⚠️ Nu s-au găsit indicatori clari de succes sau eroare\n";
}

// Verificăm dacă există notificarea de fallback
if (strpos($output, 'nu este recunoscut de API-ul SOAP pentru ședințe') !== false) {
    echo "ℹ️ Notificare fallback detectată - funcționează corect\n";
}

echo "\nTest completat!\n";
?>
