<?php
/**
 * Test script for Romanian Legal Domain Optimization
 * This script tests if all the new Romanian legal domain files are working correctly
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Romanian Legal Domain Test</h1>";

try {
    echo "<h2>1. Testing romanian_legal_domain.php</h2>";
    require_once 'includes/romanian_legal_domain.php';
    
    $terminology = RomanianLegalTerminology::getLegalTerminology();
    echo "✅ RomanianLegalTerminology loaded successfully<br>";
    echo "Civil law terms count: " . count($terminology['civil_law']['primary_terms']) . "<br>";
    
    $courts = RomanianCourtHierarchy::getCourtHierarchy();
    echo "✅ RomanianCourtHierarchy loaded successfully<br>";
    echo "Court levels count: " . count($courts) . "<br>";
    
    $geographic = RomanianGeographicData::getGeographicData();
    echo "✅ RomanianGeographicData loaded successfully<br>";
    echo "Regions count: " . count($geographic['regions']) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error in romanian_legal_domain.php: " . $e->getMessage() . "<br>";
}

try {
    echo "<h2>2. Testing romanian_structured_data.php</h2>";
    require_once 'includes/romanian_structured_data.php';
    
    $orgSchema = RomanianStructuredData::getOrganizationSchema();
    echo "✅ Organization schema generated successfully<br>";
    
    $websiteSchema = RomanianStructuredData::getWebSiteSchema();
    echo "✅ Website schema generated successfully<br>";
    
    $pageData = RomanianStructuredData::generatePageStructuredData('homepage');
    echo "✅ Page structured data generated successfully<br>";
    echo "Data length: " . strlen($pageData) . " characters<br>";
    
} catch (Exception $e) {
    echo "❌ Error in romanian_structured_data.php: " . $e->getMessage() . "<br>";
}

try {
    echo "<h2>3. Testing romanian_localization.php</h2>";
    require_once 'includes/romanian_localization.php';
    
    $date = RomanianLocalization::formatRomanianDate('2025-06-25');
    echo "✅ Date formatting works: " . $date . "<br>";
    
    $docTypes = RomanianLocalization::getLegalDocumentTypes();
    echo "✅ Legal document types loaded: " . count($docTypes) . " categories<br>";
    
    $seoTerms = RomanianLocalization::getSEOLegalTerms();
    echo "✅ SEO legal terms loaded: " . count($seoTerms['primary_keywords']) . " primary keywords<br>";
    
    $metaDesc = RomanianLocalization::generateLegalMetaDescription('homepage');
    echo "✅ Meta description generated: " . substr($metaDesc, 0, 100) . "...<br>";
    
} catch (Exception $e) {
    echo "❌ Error in romanian_localization.php: " . $e->getMessage() . "<br>";
}

try {
    echo "<h2>4. Testing functions.php dependency</h2>";
    require_once 'includes/functions.php';
    
    $instante = getInstanteList();
    echo "✅ getInstanteList() function works: " . count($instante) . " institutions<br>";
    
} catch (Exception $e) {
    echo "❌ Error in functions.php: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Testing complete integration</h2>";
try {
    // Simulate page context
    $seo_context = [
        'page_type' => 'homepage',
        'institution' => '',
        'location' => '',
        'case_type' => ''
    ];
    
    $seo_title = "Portal Judiciar România - Căutare Dosare Online | DosareJust.ro";
    $seo_description = RomanianLocalization::generateLegalMetaDescription('homepage', $seo_context);
    $seo_keywords = RomanianLocalization::generateLegalKeywords('homepage', $seo_context);
    
    $structured_data_context = [
        'breadcrumb_items' => [
            ['name' => 'Acasă', 'url' => 'https://localhost/index.php']
        ]
    ];
    $structured_data = RomanianStructuredData::generatePageStructuredData('homepage', $structured_data_context);
    
    echo "✅ Complete integration test successful<br>";
    echo "SEO Title: " . htmlspecialchars($seo_title) . "<br>";
    echo "SEO Description length: " . strlen($seo_description) . " characters<br>";
    echo "SEO Keywords count: " . count(explode(', ', $seo_keywords)) . " keywords<br>";
    echo "Structured data length: " . strlen($structured_data) . " characters<br>";
    
} catch (Exception $e) {
    echo "❌ Error in complete integration: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "If all tests show ✅, the Romanian Legal Domain Optimization is working correctly.";
?>
