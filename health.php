<?php
/**
 * Health Check Endpoint for Portal Judiciar România
 * Technical Performance Monitoring
 */

// Set JSON response header
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Start timing
$start_time = microtime(true);

// Initialize health check results
$health = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => '1.0.0',
    'environment' => 'production',
    'checks' => []
];

try {
    // 1. PHP Version Check
    $php_version = PHP_VERSION;
    $health['checks']['php'] = [
        'status' => version_compare($php_version, '7.4.0', '>=') ? 'pass' : 'fail',
        'version' => $php_version,
        'required' => '7.4.0+',
        'message' => version_compare($php_version, '7.4.0', '>=') ? 'PHP version is compatible' : 'PHP version is too old'
    ];

    // 2. Required Extensions Check
    $required_extensions = ['soap', 'mbstring', 'json', 'curl'];
    $extensions_status = 'pass';
    $missing_extensions = [];
    
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $extensions_status = 'fail';
            $missing_extensions[] = $ext;
        }
    }
    
    $health['checks']['extensions'] = [
        'status' => $extensions_status,
        'required' => $required_extensions,
        'missing' => $missing_extensions,
        'message' => $extensions_status === 'pass' ? 'All required extensions are loaded' : 'Missing extensions: ' . implode(', ', $missing_extensions)
    ];

    // 3. File System Check
    $directories = ['includes', 'services', 'assets'];
    $filesystem_status = 'pass';
    $missing_dirs = [];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            $filesystem_status = 'fail';
            $missing_dirs[] = $dir;
        }
    }
    
    $health['checks']['filesystem'] = [
        'status' => $filesystem_status,
        'directories' => $directories,
        'missing' => $missing_dirs,
        'message' => $filesystem_status === 'pass' ? 'All required directories exist' : 'Missing directories: ' . implode(', ', $missing_dirs)
    ];

    // 4. Configuration Files Check
    $config_files = ['includes/config.php', 'includes/functions.php'];
    $config_status = 'pass';
    $missing_configs = [];
    
    foreach ($config_files as $file) {
        if (!file_exists($file)) {
            $config_status = 'fail';
            $missing_configs[] = $file;
        }
    }
    
    $health['checks']['configuration'] = [
        'status' => $config_status,
        'files' => $config_files,
        'missing' => $missing_configs,
        'message' => $config_status === 'pass' ? 'All configuration files exist' : 'Missing config files: ' . implode(', ', $missing_configs)
    ];

    // 5. SOAP Service Check
    try {
        if (class_exists('SoapClient')) {
            // Test SOAP connection (without actually calling the service)
            $soap_status = 'pass';
            $soap_message = 'SOAP client is available';
        } else {
            $soap_status = 'fail';
            $soap_message = 'SOAP client is not available';
        }
    } catch (Exception $e) {
        $soap_status = 'fail';
        $soap_message = 'SOAP error: ' . $e->getMessage();
    }
    
    $health['checks']['soap'] = [
        'status' => $soap_status,
        'message' => $soap_message,
        'wsdl' => defined('SOAP_WSDL') ? 'configured' : 'not configured'
    ];

    // 6. Memory Usage Check
    $memory_limit = ini_get('memory_limit');
    $memory_usage = memory_get_usage(true);
    $memory_peak = memory_get_peak_usage(true);
    $memory_percent = ($memory_usage / parseMemoryLimit($memory_limit)) * 100;

    $health['checks']['memory'] = [
        'status' => $memory_percent < 80 ? 'pass' : ($memory_percent < 95 ? 'warning' : 'fail'),
        'usage' => formatBytes($memory_usage),
        'peak' => formatBytes($memory_peak),
        'limit' => $memory_limit,
        'percentage' => round($memory_percent, 2),
        'message' => $memory_percent < 80 ? 'Memory usage is normal' : 'High memory usage detected'
    ];

    // 7. Disk Space Check (if possible)
    $disk_free = disk_free_space('.');
    $disk_total = disk_total_space('.');
    $disk_percent = (($disk_total - $disk_free) / $disk_total) * 100;
    
    $health['checks']['disk'] = [
        'status' => $disk_percent < 90 ? 'pass' : 'warning',
        'free' => formatBytes($disk_free),
        'total' => formatBytes($disk_total),
        'used_percentage' => round($disk_percent, 2),
        'message' => $disk_percent < 90 ? 'Disk space is adequate' : 'Low disk space warning'
    ];

    // 8. Performance Metrics
    $end_time = microtime(true);
    $response_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
    
    $health['checks']['performance'] = [
        'status' => $response_time < 100 ? 'pass' : ($response_time < 500 ? 'warning' : 'fail'),
        'response_time_ms' => round($response_time, 2),
        'message' => $response_time < 100 ? 'Response time is excellent' : ($response_time < 500 ? 'Response time is acceptable' : 'Slow response time detected')
    ];

    // Determine overall status
    $overall_status = 'healthy';
    foreach ($health['checks'] as $check) {
        if ($check['status'] === 'fail') {
            $overall_status = 'unhealthy';
            break;
        } elseif ($check['status'] === 'warning' && $overall_status === 'healthy') {
            $overall_status = 'degraded';
        }
    }
    
    $health['status'] = $overall_status;
    $health['response_time_ms'] = round($response_time, 2);

} catch (Exception $e) {
    $health['status'] = 'unhealthy';
    $health['error'] = $e->getMessage();
    http_response_code(500);
}

// Helper functions
function parseMemoryLimit($limit) {
    $limit = trim($limit);
    $last = strtolower($limit[strlen($limit)-1]);
    $limit = (int) $limit;
    switch($last) {
        case 'g': $limit *= 1024;
        case 'm': $limit *= 1024;
        case 'k': $limit *= 1024;
    }
    return $limit;
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Set appropriate HTTP status code
switch ($health['status']) {
    case 'healthy':
        http_response_code(200);
        break;
    case 'degraded':
        http_response_code(200); // Still operational
        break;
    case 'unhealthy':
        http_response_code(503); // Service unavailable
        break;
}

// Output JSON response
echo json_encode($health, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
